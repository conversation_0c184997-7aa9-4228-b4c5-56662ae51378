<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}LandHub - Land Real Estate Platform{% endblock %}</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Unpoly -->
    <script src="https://unpkg.com/unpoly@3.7.2/unpoly.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/unpoly@3.7.2/unpoly.min.css">
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                            950: '#082f49'
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a'
                        },
                        accent: {
                            50: '#fefce8',
                            100: '#fef9c3',
                            200: '#fef08a',
                            300: '#fde047',
                            400: '#facc15',
                            500: '#eab308',
                            600: '#ca8a04',
                            700: '#a16207',
                            800: '#854d0e',
                            900: '#713f12'
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'slide-down': 'slideDown 0.3s ease-out',
                        'scale-in': 'scaleIn 0.2s ease-out',
                        'bounce-subtle': 'bounceSubtle 0.6s ease-out'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        slideDown: {
                            '0%': { transform: 'translateY(-10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.95)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' }
                        },
                        bounceSubtle: {
                            '0%, 20%, 53%, 80%, 100%': { transform: 'translate3d(0,0,0)' },
                            '40%, 43%': { transform: 'translate3d(0, -8px, 0)' },
                            '70%': { transform: 'translate3d(0, -4px, 0)' },
                            '90%': { transform: 'translate3d(0, -2px, 0)' }
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        [x-cloak] { display: none !important; }

        /* Base styles */
        body {
            font-family: 'Inter', system-ui, sans-serif;
            font-feature-settings: 'cv11', 'ss01';
            font-variation-settings: 'opsz' 32;
        }

        .font-display {
            font-family: 'Poppins', system-ui, sans-serif;
        }

        /* Enhanced button styles */
        .btn {
            @apply inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
        }

        .btn-primary {
            @apply btn bg-primary-600 hover:bg-primary-700 active:bg-primary-800 text-white shadow-sm hover:shadow-md focus:ring-primary-500;
        }

        .btn-secondary {
            @apply btn bg-white hover:bg-secondary-50 active:bg-secondary-100 text-secondary-900 border border-secondary-300 hover:border-secondary-400 shadow-sm hover:shadow-md focus:ring-secondary-500;
        }

        .btn-danger {
            @apply btn bg-red-600 hover:bg-red-700 active:bg-red-800 text-white shadow-sm hover:shadow-md focus:ring-red-500;
        }

        .btn-ghost {
            @apply btn bg-transparent hover:bg-secondary-100 active:bg-secondary-200 text-secondary-700 hover:text-secondary-900 focus:ring-secondary-500;
        }

        .btn-sm {
            @apply px-3 py-1.5 text-sm rounded-md;
        }

        .btn-md {
            @apply px-4 py-2 text-sm rounded-lg;
        }

        .btn-lg {
            @apply px-6 py-3 text-base rounded-lg;
        }

        /* Enhanced form styles */
        .form-input {
            @apply w-full px-4 py-3 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 bg-white transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-secondary-400;
        }

        .form-textarea {
            @apply form-input resize-vertical min-h-[100px];
        }

        .form-select {
            @apply form-input cursor-pointer;
        }

        .form-label {
            @apply block text-sm font-medium text-secondary-700 mb-2;
        }

        .form-error {
            @apply mt-1 text-sm text-red-600;
        }

        /* Enhanced card styles */
        .card {
            @apply bg-white rounded-xl shadow-sm border border-secondary-200 overflow-hidden transition-all duration-200 hover:shadow-md;
        }

        .card-elevated {
            @apply shadow-lg hover:shadow-xl;
        }

        .card-header {
            @apply px-6 py-5 border-b border-secondary-200 bg-secondary-50/50;
        }

        .card-body {
            @apply px-6 py-5;
        }

        .card-footer {
            @apply px-6 py-4 border-t border-secondary-200 bg-secondary-50/30;
        }

        /* Navigation enhancements */
        .nav-link {
            @apply relative px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg;
        }

        .nav-link-active {
            @apply bg-primary-100 text-primary-700;
        }

        .nav-link-inactive {
            @apply text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100;
        }

        /* Alert styles */
        .alert {
            @apply p-4 rounded-lg mb-4 border-l-4;
        }

        .alert-success {
            @apply bg-green-50 border-green-400 text-green-800;
        }

        .alert-error {
            @apply bg-red-50 border-red-400 text-red-800;
        }

        .alert-warning {
            @apply bg-yellow-50 border-yellow-400 text-yellow-800;
        }

        .alert-info {
            @apply bg-blue-50 border-blue-400 text-blue-800;
        }

        /* Loading states */
        .loading {
            @apply opacity-50 pointer-events-none;
        }

        .spinner {
            @apply animate-spin rounded-full border-2 border-secondary-300 border-t-primary-600;
        }

        /* Utility classes */
        .glass {
            @apply bg-white/80 backdrop-blur-sm border border-white/20;
        }

        .gradient-primary {
            background: linear-gradient(135deg, theme('colors.primary.600') 0%, theme('colors.primary.700') 100%);
        }

        .gradient-secondary {
            background: linear-gradient(135deg, theme('colors.secondary.100') 0%, theme('colors.secondary.200') 100%);
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Focus styles */
        .focus-ring {
            @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
        }
    </style>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gradient-to-br from-secondary-50 to-secondary-100 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white/95 backdrop-blur-sm shadow-sm border-b border-secondary-200 sticky top-0 z-50" x-data="{ mobileMenuOpen: false }">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{% url 'home' %}" class="flex items-center space-x-2 text-xl font-display font-bold text-primary-600 hover:text-primary-700 transition-colors duration-200">
                        <svg class="w-8 h-8" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                        </svg>
                        <span>LandHub</span>
                    </a>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button @click="mobileMenuOpen = !mobileMenuOpen" class="inline-flex items-center justify-center p-2 rounded-md text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path x-show="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            <path x-show="mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Desktop navigation -->
                <div class="hidden md:flex items-center space-x-4">
                    {% if user.is_authenticated %}
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-secondary-700 hover:text-secondary-900 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200">
                                {% if user.profile.avatar %}
                                    <img src="{{ user.profile.avatar.url }}" alt="Avatar" class="w-8 h-8 rounded-full object-cover">
                                {% else %}
                                    <div class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center">
                                        <span class="text-primary-600 font-medium text-sm">{{ user.first_name.0|default:user.username.0|upper }}</span>
                                    </div>
                                {% endif %}
                                <span>{{ user.first_name|default:user.username }}</span>
                                <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <div x-show="open" @click.away="open = false" x-cloak
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 scale-95"
                                 x-transition:enter-end="opacity-100 scale-100"
                                 x-transition:leave="transition ease-in duration-150"
                                 x-transition:leave-start="opacity-100 scale-100"
                                 x-transition:leave-end="opacity-0 scale-95"
                                 class="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg border border-secondary-200 py-2 z-50">
                                <div class="px-4 py-3 border-b border-secondary-100">
                                    <p class="text-sm font-medium text-secondary-900">{{ user.get_full_name|default:user.username }}</p>
                                    <p class="text-xs text-secondary-500">{{ user.email }}</p>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800 mt-1">
                                        {{ user.profile.get_role_display }}
                                    </span>
                                </div>
                                <a href="{% url 'auth:profile' %}" class="flex items-center px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50 hover:text-secondary-900 transition-colors duration-200">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    Profile
                                </a>
                                <a href="{% url 'auth:dashboard_redirect' %}" class="flex items-center px-4 py-2 text-sm text-secondary-700 hover:bg-secondary-50 hover:text-secondary-900 transition-colors duration-200">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    Dashboard
                                </a>
                                <hr class="my-2 border-secondary-100">
                                <a href="{% url 'auth:logout' %}" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200">
                                    <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                    </svg>
                                    Logout
                                </a>
                            </div>
                        </div>
                    {% else %}
                        <a href="{% url 'auth:login' %}" class="nav-link nav-link-inactive">Login</a>
                        <a href="{% url 'auth:register' %}" class="btn btn-primary btn-md">Register</a>
                    {% endif %}
                </div>
            </div>

            <!-- Mobile menu -->
            <div x-show="mobileMenuOpen" x-cloak
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 scale-95"
                 x-transition:enter-end="opacity-100 scale-100"
                 x-transition:leave="transition ease-in duration-150"
                 x-transition:leave-start="opacity-100 scale-100"
                 x-transition:leave-end="opacity-0 scale-95"
                 class="md:hidden border-t border-secondary-200 bg-white">
                <div class="px-4 py-3 space-y-2">
                    {% if user.is_authenticated %}
                        <div class="flex items-center space-x-3 pb-3 border-b border-secondary-100">
                            {% if user.profile.avatar %}
                                <img src="{{ user.profile.avatar.url }}" alt="Avatar" class="w-10 h-10 rounded-full object-cover">
                            {% else %}
                                <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center">
                                    <span class="text-primary-600 font-medium">{{ user.first_name.0|default:user.username.0|upper }}</span>
                                </div>
                            {% endif %}
                            <div>
                                <p class="font-medium text-secondary-900">{{ user.get_full_name|default:user.username }}</p>
                                <p class="text-sm text-secondary-500">{{ user.email }}</p>
                            </div>
                        </div>
                        <a href="{% url 'auth:profile' %}" class="block px-3 py-2 text-base font-medium text-secondary-700 hover:text-secondary-900 hover:bg-secondary-50 rounded-lg">Profile</a>
                        <a href="{% url 'auth:dashboard_redirect' %}" class="block px-3 py-2 text-base font-medium text-secondary-700 hover:text-secondary-900 hover:bg-secondary-50 rounded-lg">Dashboard</a>
                        <a href="{% url 'auth:logout' %}" class="block px-3 py-2 text-base font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg">Logout</a>
                    {% else %}
                        <a href="{% url 'auth:login' %}" class="block px-3 py-2 text-base font-medium text-secondary-700 hover:text-secondary-900 hover:bg-secondary-50 rounded-lg">Login</a>
                        <a href="{% url 'auth:register' %}" class="block px-3 py-2 text-base font-medium bg-primary-600 text-white hover:bg-primary-700 rounded-lg text-center">Register</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="fixed top-20 right-4 z-50 space-y-2" x-data="{ messages: [] }" x-init="
            {% for message in messages %}
                messages.push({
                    id: {{ forloop.counter }},
                    type: '{{ message.tags|default:'info' }}',
                    text: '{{ message|escapejs }}',
                    show: true
                });
            {% endfor %}
        ">
            <template x-for="message in messages.filter(m => m.show)" :key="message.id">
                <div x-show="message.show"
                     x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform translate-x-full"
                     x-transition:enter-end="opacity-100 transform translate-x-0"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform translate-x-0"
                     x-transition:leave-end="opacity-0 transform translate-x-full"
                     :class="{
                         'alert-success': message.type === 'success',
                         'alert-error': message.type === 'error',
                         'alert-warning': message.type === 'warning',
                         'alert-info': message.type === 'info'
                     }"
                     class="alert max-w-sm shadow-lg">
                    <div class="flex justify-between items-start">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <svg x-show="message.type === 'success'" class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <svg x-show="message.type === 'error'" class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                                <svg x-show="message.type === 'warning'" class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <svg x-show="message.type === 'info'" class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <p class="text-sm font-medium" x-text="message.text"></p>
                        </div>
                        <button @click="message.show = false" class="ml-4 flex-shrink-0 text-secondary-400 hover:text-secondary-600 transition-colors duration-200">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </template>
        </div>
    {% endif %}

    <!-- Main Content -->
    <main class="{% block main_class %}max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8{% endblock %} animate-fade-in">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-white/95 backdrop-blur-sm border-t border-secondary-200 mt-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center space-x-2 mb-4">
                        <svg class="w-8 h-8 text-primary-600" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                        </svg>
                        <span class="text-xl font-display font-bold text-secondary-900">LandHub</span>
                    </div>
                    <p class="text-secondary-600 mb-4 max-w-md">
                        Discover premium land opportunities for residential, commercial, agricultural, and recreational use. Connect with verified sellers and find your ideal property.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-secondary-400 hover:text-primary-600 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-secondary-400 hover:text-primary-600 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-secondary-400 hover:text-primary-600 transition-colors duration-200">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="text-sm font-semibold text-secondary-900 uppercase tracking-wider mb-4">Platform</h3>
                    <ul class="space-y-3">
                        <li><a href="{% url 'home' %}" class="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Browse Listings</a></li>
                        <li><a href="#" class="text-secondary-600 hover:text-primary-600 transition-colors duration-200">How It Works</a></li>
                        <li><a href="#" class="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Pricing</a></li>
                        <li><a href="#" class="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Success Stories</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-sm font-semibold text-secondary-900 uppercase tracking-wider mb-4">Support</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Help Center</a></li>
                        <li><a href="#" class="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Contact Us</a></li>
                        <li><a href="#" class="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Privacy Policy</a></li>
                        <li><a href="#" class="text-secondary-600 hover:text-primary-600 transition-colors duration-200">Terms of Service</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-secondary-200 mt-8 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-secondary-500 text-sm">&copy; 2025 LandHub. All rights reserved.</p>
                    <p class="text-secondary-500 text-sm mt-2 md:mt-0">Made with ❤️ for land enthusiasts</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Enhanced JavaScript -->
    <script>
        // Configure HTMX with enhanced loading states
        document.body.addEventListener('htmx:configRequest', function(evt) {
            evt.detail.headers['X-CSRFToken'] = '{{ csrf_token }}';
        });

        // Enhanced loading states with spinners
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            const target = evt.target;
            if (target.classList.contains('btn')) {
                target.classList.add('loading');
                target.disabled = true;

                // Add spinner if it's a button
                const originalText = target.innerHTML;
                target.dataset.originalText = originalText;
                target.innerHTML = `
                    <div class="flex items-center justify-center">
                        <div class="spinner w-4 h-4 mr-2"></div>
                        <span>Loading...</span>
                    </div>
                `;
            }
        });

        document.body.addEventListener('htmx:afterRequest', function(evt) {
            const target = evt.target;
            if (target.classList.contains('btn')) {
                target.classList.remove('loading');
                target.disabled = false;

                // Restore original text
                if (target.dataset.originalText) {
                    target.innerHTML = target.dataset.originalText;
                    delete target.dataset.originalText;
                }
            }
        });

        // Auto-hide messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                const messages = document.querySelectorAll('[x-data*="messages"]');
                messages.forEach(messageContainer => {
                    if (messageContainer._x_dataStack && messageContainer._x_dataStack[0].messages) {
                        messageContainer._x_dataStack[0].messages.forEach(message => {
                            setTimeout(() => {
                                message.show = false;
                            }, 5000);
                        });
                    }
                });
            }, 100);
        });

        // Smooth scroll for anchor links
        document.addEventListener('click', function(e) {
            if (e.target.matches('a[href^="#"]')) {
                e.preventDefault();
                const target = document.querySelector(e.target.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });

        // Enhanced form validation feedback
        document.addEventListener('input', function(e) {
            if (e.target.matches('.form-input, .form-textarea, .form-select')) {
                const input = e.target;
                const isValid = input.checkValidity();

                // Remove existing validation classes
                input.classList.remove('border-red-500', 'border-green-500', 'focus:ring-red-500', 'focus:ring-green-500');

                if (input.value.length > 0) {
                    if (isValid) {
                        input.classList.add('border-green-500', 'focus:ring-green-500');
                    } else {
                        input.classList.add('border-red-500', 'focus:ring-red-500');
                    }
                }
            }
        });

        // Keyboard navigation improvements
        document.addEventListener('keydown', function(e) {
            // Escape key closes dropdowns
            if (e.key === 'Escape') {
                // Close Alpine.js dropdowns
                document.querySelectorAll('[x-data]').forEach(el => {
                    if (el._x_dataStack && el._x_dataStack[0].open !== undefined) {
                        el._x_dataStack[0].open = false;
                    }
                    if (el._x_dataStack && el._x_dataStack[0].mobileMenuOpen !== undefined) {
                        el._x_dataStack[0].mobileMenuOpen = false;
                    }
                });
            }
        });

        // Page transition effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add entrance animation to main content
            const main = document.querySelector('main');
            if (main) {
                main.style.opacity = '0';
                main.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    main.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
                    main.style.opacity = '1';
                    main.style.transform = 'translateY(0)';
                }, 100);
            }
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-slide-up');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe cards and other elements for animation
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.card, .alert').forEach(el => {
                observer.observe(el);
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>