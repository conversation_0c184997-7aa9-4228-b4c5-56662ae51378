<!-- Modern Button Component -->
<!-- Usage: {% include 'components/button.html' with variant='primary' size='md' text='Click me' href='#' %} -->

{% load static %}

{% if href %}
    <a href="{{ href }}" 
       class="btn btn-{{ variant|default:'primary' }} btn-{{ size|default:'md' }} {{ class|default:'' }}"
       {% if target %}target="{{ target }}"{% endif %}
       {% if disabled %}aria-disabled="true"{% endif %}
       {% if id %}id="{{ id }}"{% endif %}
       {% if hx_post %}hx-post="{{ hx_post }}"{% endif %}
       {% if hx_get %}hx-get="{{ hx_get }}"{% endif %}
       {% if hx_target %}hx-target="{{ hx_target }}"{% endif %}
       {% if hx_swap %}hx-swap="{{ hx_swap }}"{% endif %}
       {% if onclick %}onclick="{{ onclick }}"{% endif %}>
        {% if icon_left %}
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {{ icon_left|safe }}
            </svg>
        {% endif %}
        {{ text|default:content }}
        {% if icon_right %}
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {{ icon_right|safe }}
            </svg>
        {% endif %}
    </a>
{% else %}
    <button type="{{ type|default:'button' }}" 
            class="btn btn-{{ variant|default:'primary' }} btn-{{ size|default:'md' }} {{ class|default:'' }}"
            {% if disabled %}disabled{% endif %}
            {% if id %}id="{{ id }}"{% endif %}
            {% if hx_post %}hx-post="{{ hx_post }}"{% endif %}
            {% if hx_get %}hx-get="{{ hx_get }}"{% endif %}
            {% if hx_target %}hx-target="{{ hx_target }}"{% endif %}
            {% if hx_swap %}hx-swap="{{ hx_swap }}"{% endif %}
            {% if onclick %}onclick="{{ onclick }}"{% endif %}
            {% if x_data %}x-data="{{ x_data }}"{% endif %}
            {% if x_click %}@click="{{ x_click }}"{% endif %}>
        {% if icon_left %}
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {{ icon_left|safe }}
            </svg>
        {% endif %}
        {{ text|default:content }}
        {% if icon_right %}
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {{ icon_right|safe }}
            </svg>
        {% endif %}
    </button>
{% endif %}
