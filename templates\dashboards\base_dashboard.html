{% extends 'base.html' %}

{% block title %}{% block dashboard_title %}Dashboard{% endblock %} - LandHub{% endblock %}

{% block main_class %}{% endblock %}

{% block content %}
<div class="flex h-screen bg-secondary-50" x-data="{ sidebarOpen: false }">
    <!-- Desktop Sidebar -->
    <div class="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0 lg:pt-16 lg:z-30">
        <div class="flex flex-col flex-grow bg-white border-r border-secondary-200 shadow-sm overflow-y-auto">
            <!-- Sidebar Header -->
            <div class="flex items-center justify-between flex-shrink-0 px-6 py-4 border-b border-secondary-200">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center shadow-sm">
                        {% block sidebar_icon %}
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        {% endblock %}
                    </div>
                    <div>
                        <h2 class="text-lg font-display font-semibold text-secondary-900">{% block sidebar_title %}Dashboard{% endblock %}</h2>
                        <p class="text-xs text-secondary-500 capitalize">{{ user.profile.get_role_display }}</p>
                    </div>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="flex-1 px-4 py-6 space-y-2">
                {% block sidebar_navigation %}
                <!-- Role-specific navigation will be defined in child templates -->
                {% endblock %}
            </nav>

            <!-- Sidebar Footer -->
            <div class="flex-shrink-0 border-t border-secondary-200 p-4">
                <div class="flex items-center space-x-3">
                    {% if user.profile.avatar %}
                        <img src="{{ user.profile.avatar.url }}" alt="Avatar" class="w-8 h-8 rounded-full object-cover">
                    {% else %}
                        <div class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center">
                            <span class="text-primary-600 font-medium text-sm">{{ user.first_name.0|default:user.username.0|upper }}</span>
                        </div>
                    {% endif %}
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-secondary-900 truncate">{{ user.get_full_name|default:user.username }}</p>
                        <p class="text-xs text-secondary-500 truncate">{{ user.email }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div x-show="sidebarOpen" 
         x-cloak
         class="fixed inset-0 flex z-40 lg:hidden"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0">
        
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-secondary-600 bg-opacity-75" 
             @click="sidebarOpen = false"
             aria-hidden="true"></div>

        <!-- Mobile Sidebar -->
        <div class="relative flex-1 flex flex-col max-w-xs w-full bg-white shadow-xl"
             x-transition:enter="transition ease-in-out duration-300 transform"
             x-transition:enter-start="-translate-x-full"
             x-transition:enter-end="translate-x-0"
             x-transition:leave="transition ease-in-out duration-300 transform"
             x-transition:leave-start="translate-x-0"
             x-transition:leave-end="-translate-x-full">
            
            <!-- Close Button -->
            <div class="absolute top-0 right-0 -mr-12 pt-2">
                <button @click="sidebarOpen = false" 
                        class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white transition-colors duration-200 hover:bg-white hover:bg-opacity-20">
                    <span class="sr-only">Close sidebar</span>
                    <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Sidebar Content -->
            <div class="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto">
                <!-- Mobile Sidebar Header -->
                <div class="flex items-center justify-between flex-shrink-0 px-6 pb-4 border-b border-secondary-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center shadow-sm">
                            {% block mobile_sidebar_icon %}
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            {% endblock %}
                        </div>
                        <div>
                            <h2 class="text-lg font-display font-semibold text-secondary-900">{% block mobile_sidebar_title %}Dashboard{% endblock %}</h2>
                            <p class="text-xs text-secondary-500 capitalize">{{ user.profile.get_role_display }}</p>
                        </div>
                    </div>
                </div>

                <!-- Mobile Navigation Menu -->
                <nav class="flex-1 px-4 py-6 space-y-2">
                    {% block mobile_sidebar_navigation %}
                    {% block sidebar_navigation %}
                    <!-- Role-specific navigation will be defined in child templates -->
                    {% endblock %}
                    {% endblock %}
                </nav>

                <!-- Mobile Sidebar Footer -->
                <div class="flex-shrink-0 border-t border-secondary-200 p-4">
                    <div class="flex items-center space-x-3">
                        {% if user.profile.avatar %}
                            <img src="{{ user.profile.avatar.url }}" alt="Avatar" class="w-10 h-10 rounded-full object-cover">
                        {% else %}
                            <div class="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center">
                                <span class="text-primary-600 font-medium">{{ user.first_name.0|default:user.username.0|upper }}</span>
                            </div>
                        {% endif %}
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-secondary-900 truncate">{{ user.get_full_name|default:user.username }}</p>
                            <p class="text-xs text-secondary-500 truncate">{{ user.email }}</p>
                        </div>
                    </div>
                    
                    <!-- Mobile Quick Actions -->
                    <div class="mt-4 space-y-2">
                        <a href="{% url 'auth:profile' %}" 
                           class="flex items-center px-3 py-2 text-sm text-secondary-700 hover:bg-secondary-50 hover:text-secondary-900 rounded-lg transition-colors duration-200">
                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Profile Settings
                        </a>
                        <a href="{% url 'auth:logout' %}" 
                           class="flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50 hover:text-red-700 rounded-lg transition-colors duration-200">
                            <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            Sign Out
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex flex-col flex-1 lg:pl-64">
        <!-- Mobile Header -->
        <div class="lg:hidden bg-white border-b border-secondary-200 px-4 py-3 shadow-sm">
            <div class="flex items-center justify-between">
                <!-- Mobile Menu Button -->
                <button @click="sidebarOpen = true" 
                        class="inline-flex items-center justify-center p-2 rounded-lg text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors duration-200">
                    <span class="sr-only">Open sidebar</span>
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>

                <!-- Mobile Header Title -->
                <h1 class="text-lg font-display font-semibold text-secondary-900">{% block mobile_header_title %}{% block dashboard_title %}Dashboard{% endblock %}{% endblock %}</h1>

                <!-- Mobile User Avatar -->
                <div class="flex items-center">
                    {% if user.profile.avatar %}
                        <img src="{{ user.profile.avatar.url }}" alt="Avatar" class="w-8 h-8 rounded-full object-cover">
                    {% else %}
                        <div class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center">
                            <span class="text-primary-600 font-medium text-sm">{{ user.first_name.0|default:user.username.0|upper }}</span>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <main class="flex-1 overflow-y-auto bg-secondary-50">
            <div class="py-6">
                <!-- Page Header -->
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex items-center justify-between">
                        <div>
                            <h1 class="text-2xl font-display font-bold text-secondary-900">{% block page_title %}{% block dashboard_title %}Dashboard{% endblock %}{% endblock %}</h1>
                            <p class="mt-1 text-sm text-secondary-600">{% block page_description %}Welcome to your dashboard{% endblock %}</p>
                        </div>
                        <div class="flex items-center space-x-3">
                            {% block page_actions %}
                            <!-- Page-specific actions will be defined in child templates -->
                            {% endblock %}
                        </div>
                    </div>
                </div>

                <!-- Page Content -->
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
                    {% block dashboard_content %}
                    <!-- Dashboard-specific content will be defined in child templates -->
                    {% endblock %}
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Custom Alpine.js Components -->
<script>
document.addEventListener('alpine:init', () => {
    // Sidebar navigation component
    Alpine.data('sidebarNav', () => ({
        activeItem: '{% block active_nav_item %}overview{% endblock %}',
        
        isActive(item) {
            return this.activeItem === item;
        },
        
        setActive(item) {
            this.activeItem = item;
        }
    }));
});
</script>
{% endblock %}