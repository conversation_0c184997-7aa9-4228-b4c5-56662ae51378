# Generated by Django 5.2.4 on 2025-07-26 13:04

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('landmarket', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Land',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=200)),
                ('description', models.TextField()),
                ('price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('size_acres', models.DecimalField(decimal_places=2, max_digits=10)),
                ('location', models.CharField(max_length=200)),
                ('address', models.TextField()),
                ('property_type', models.CharField(choices=[('residential', 'Residential'), ('commercial', 'Commercial'), ('agricultural', 'Agricultural'), ('recreational', 'Recreational')], max_length=20)),
                ('status', models.Char<PERSON>ield(choices=[('draft', 'Draft'), ('pending', 'Pending Approval'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('sold', 'Sold')], default='draft', max_length=20)),
                ('is_approved', models.BooleanField(default=False)),
                ('admin_notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='land_listings', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Land Listing',
                'verbose_name_plural': 'Land Listings',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Inquiry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_read', models.BooleanField(default=False)),
                ('seller_response', models.TextField(blank=True)),
                ('response_date', models.DateTimeField(blank=True, null=True)),
                ('buyer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inquiries_sent', to=settings.AUTH_USER_MODEL)),
                ('land', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inquiries', to='landmarket.land')),
            ],
            options={
                'verbose_name': 'Inquiry',
                'verbose_name_plural': 'Inquiries',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LandImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='listings/')),
                ('alt_text', models.CharField(blank=True, max_length=200)),
                ('is_primary', models.BooleanField(default=False)),
                ('order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('land', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='landmarket.land')),
            ],
            options={
                'verbose_name': 'Land Image',
                'verbose_name_plural': 'Land Images',
                'ordering': ['order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Favorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to=settings.AUTH_USER_MODEL)),
                ('land', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='landmarket.land')),
            ],
            options={
                'verbose_name': 'Favorite',
                'verbose_name_plural': 'Favorites',
                'ordering': ['-created_at'],
                'unique_together': {('user', 'land')},
            },
        ),
    ]
