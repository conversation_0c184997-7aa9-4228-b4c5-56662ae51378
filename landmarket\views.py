from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q, Count
from django.core.paginator import Paginator
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from .models import UserProfile, Land, Inquiry, Favorite
import json


def home(request):
    """Home page view"""
    return render(request, 'home.html')


@login_required
def admin_dashboard(request):
    """Admin dashboard view with analytics"""
    if not request.user.profile.role == 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get analytics data
    total_users = User.objects.count()
    pending_listings = Land.objects.filter(status='pending').count()
    active_listings = Land.objects.filter(status='approved').count()
    total_inquiries = Inquiry.objects.count()
    
    # Recent activity (last 10 activities)
    recent_users = User.objects.order_by('-date_joined')[:5]
    recent_listings = Land.objects.order_by('-created_at')[:5]
    recent_inquiries = Inquiry.objects.order_by('-created_at')[:5]
    
    context = {
        'total_users': total_users,
        'pending_listings': pending_listings,
        'active_listings': active_listings,
        'total_inquiries': total_inquiries,
        'recent_users': recent_users,
        'recent_listings': recent_listings,
        'recent_inquiries': recent_inquiries,
    }
    
    return render(request, 'dashboards/admin_dashboard.html', context)


@login_required
def admin_user_management(request):
    """Admin user management view with search and filtering"""
    if not request.user.profile.role == 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get search and filter parameters
    search_query = request.GET.get('search', '')
    role_filter = request.GET.get('role', '')
    status_filter = request.GET.get('status', '')
    
    # Build queryset
    users = User.objects.select_related('profile').all()
    
    if search_query:
        users = users.filter(
            Q(username__icontains=search_query) |
            Q(first_name__icontains=search_query) |
            Q(last_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )
    
    if role_filter:
        users = users.filter(profile__role=role_filter)
    
    if status_filter == 'active':
        users = users.filter(is_active=True)
    elif status_filter == 'inactive':
        users = users.filter(is_active=False)
    
    # Pagination
    paginator = Paginator(users, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'role_filter': role_filter,
        'status_filter': status_filter,
        'role_choices': UserProfile.ROLE_CHOICES,
    }
    
    return render(request, 'dashboards/admin_user_management.html', context)


@login_required
@require_http_methods(["POST"])
def admin_toggle_user_status(request, user_id):
    """Toggle user active status via HTMX"""
    if not request.user.profile.role == 'admin':
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    user = get_object_or_404(User, id=user_id)
    user.is_active = not user.is_active
    user.save()
    
    status_text = 'Active' if user.is_active else 'Inactive'
    status_class = 'bg-green-100 text-green-800' if user.is_active else 'bg-red-100 text-red-800'
    
    return render(request, 'components/user_status_badge.html', {
        'user': user,
        'status_text': status_text,
        'status_class': status_class
    })


@login_required
def admin_listing_approval(request):
    """Admin listing approval queue"""
    if not request.user.profile.role == 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # Get filter parameters
    status_filter = request.GET.get('status', 'pending')
    search_query = request.GET.get('search', '')
    
    # Build queryset
    listings = Land.objects.select_related('owner', 'owner__profile').all()
    
    if status_filter:
        listings = listings.filter(status=status_filter)
    
    if search_query:
        listings = listings.filter(
            Q(title__icontains=search_query) |
            Q(location__icontains=search_query) |
            Q(owner__username__icontains=search_query)
        )
    
    # Pagination
    paginator = Paginator(listings, 15)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'search_query': search_query,
        'status_choices': Land.STATUS_CHOICES,
    }
    
    return render(request, 'dashboards/admin_listing_approval.html', context)


@login_required
@require_http_methods(["POST"])
def admin_approve_listing(request, listing_id):
    """Approve listing via HTMX"""
    if not request.user.profile.role == 'admin':
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    listing = get_object_or_404(Land, id=listing_id)
    listing.status = 'approved'
    listing.is_approved = True
    admin_notes_input = request.POST.get('admin_notes', '')
    if admin_notes_input:
        listing.admin_notes = admin_notes_input
    listing.save()
    
    return render(request, 'components/listing_status_badge.html', {
        'listing': listing
    })


@login_required
@require_http_methods(["POST"])
def admin_reject_listing(request, listing_id):
    """Reject listing via HTMX"""
    if not request.user.profile.role == 'admin':
        return JsonResponse({'error': 'Access denied'}, status=403)
    
    listing = get_object_or_404(Land, id=listing_id)
    listing.status = 'rejected'
    listing.is_approved = False
    admin_notes_input = request.POST.get('admin_notes', '')
    if admin_notes_input:
        listing.admin_notes = admin_notes_input
    listing.save()
    
    return render(request, 'components/listing_status_badge.html', {
        'listing': listing
    })


@login_required
def admin_analytics(request):
    """Admin analytics and reports dashboard"""
    if not request.user.profile.role == 'admin':
        messages.error(request, 'Access denied. Admin privileges required.')
        return redirect('auth:dashboard_redirect')
    
    # User statistics
    user_stats = {
        'total_users': User.objects.count(),
        'active_users': User.objects.filter(is_active=True).count(),
        'inactive_users': User.objects.filter(is_active=False).count(),
        'users_by_role': User.objects.values('profile__role').annotate(count=Count('id')),
    }
    
    # Listing statistics
    listing_stats = {
        'total_listings': Land.objects.count(),
        'pending_listings': Land.objects.filter(status='pending').count(),
        'approved_listings': Land.objects.filter(status='approved').count(),
        'rejected_listings': Land.objects.filter(status='rejected').count(),
        'sold_listings': Land.objects.filter(status='sold').count(),
        'listings_by_type': Land.objects.values('property_type').annotate(count=Count('id')),
    }
    
    # Inquiry statistics
    inquiry_stats = {
        'total_inquiries': Inquiry.objects.count(),
        'unread_inquiries': Inquiry.objects.filter(is_read=False).count(),
        'responded_inquiries': Inquiry.objects.exclude(seller_response='').count(),
    }
    
    context = {
        'user_stats': user_stats,
        'listing_stats': listing_stats,
        'inquiry_stats': inquiry_stats,
    }
    
    return render(request, 'dashboards/admin_analytics.html', context)


@login_required
def seller_dashboard(request):
    """Seller dashboard view"""
    if not request.user.profile.role == 'seller':
        messages.error(request, 'Access denied. Seller privileges required.')
        return redirect('auth:dashboard_redirect')
    return render(request, 'dashboards/seller_dashboard.html')


@login_required
def buyer_dashboard(request):
    """Buyer dashboard view"""
    if not request.user.profile.role == 'buyer':
        messages.error(request, 'Access denied. Buyer privileges required.')
        return redirect('auth:dashboard_redirect')
    return render(request, 'dashboards/buyer_dashboard.html')
