{% extends 'base.html' %}

{% block title %}Set New Password - LandHub{% endblock %}

{% block content %}
<div class="max-w-md mx-auto bg-white rounded-lg shadow-sm border p-8">
    <div class="text-center mb-8">
        <h1 class="text-2xl font-bold text-gray-900">Set New Password</h1>
        <p class="text-gray-600 mt-2">Enter your new password below</p>
    </div>
    
    {% if validlink %}
        <form method="post">
            {% csrf_token %}
            <div class="mb-4">
                <label for="{{ form.new_password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    New Password
                </label>
                {{ form.new_password1 }}
                {% if form.new_password1.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.new_password1.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <div class="mb-6">
                <label for="{{ form.new_password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                    Confirm New Password
                </label>
                {{ form.new_password2 }}
                {% if form.new_password2.errors %}
                    <div class="mt-1 text-sm text-red-600">
                        {{ form.new_password2.errors.0 }}
                    </div>
                {% endif %}
            </div>
            
            <button type="submit" class="btn-primary w-full">
                Set New Password
            </button>
        </form>
    {% else %}
        <div class="text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <h2 class="text-xl font-semibold text-gray-900 mb-2">Invalid Reset Link</h2>
            <p class="text-gray-600 mb-6">
                This password reset link is invalid or has expired. 
                Please request a new password reset.
            </p>
            <a href="{% url 'auth:password_reset' %}" class="btn-primary">
                Request New Reset
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}