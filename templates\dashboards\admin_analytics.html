{% extends 'dashboards/base_dashboard.html' %}

{% block dashboard_title %}Analytics & Reports{% endblock %}
{% block active_nav_item %}reports{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Admin Panel{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='/admin-dashboard/' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Overview' item_key='overview' %}

    <!-- User Management -->
    {% include 'components/sidebar_nav_item.html' with href='/admin/users/' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>' label='User Management' item_key='users' %}

    <!-- Listing Approval -->
    {% include 'components/sidebar_nav_item.html' with href='/admin/listings/' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>' label='Listing Approval' item_key='listings' %}

    <!-- Reports & Analytics -->
    {% include 'components/sidebar_nav_item.html' with href='/admin/analytics/' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Reports & Analytics' item_key='reports' active=True %}
</div>
{% endblock %}

{% block page_title %}Analytics & Reports{% endblock %}
{% block page_description %}Platform performance metrics and detailed analytics{% endblock %}

{% block page_actions %}
<button class="btn btn-secondary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    Export Report
</button>
<button class="btn btn-primary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
    </svg>
    Refresh Data
</button>
{% endblock %}

{% block dashboard_content %}
<!-- User Statistics -->
<div class="mb-8">
    <h2 class="text-xl font-display font-semibold text-secondary-900 mb-4">User Statistics</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Total Users</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ user_stats.total_users }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Users -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Active Users</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ user_stats.active_users }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inactive Users -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Inactive Users</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ user_stats.inactive_users }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users by Role -->
        <div class="card">
            <div class="card-body">
                <h3 class="text-sm font-medium text-secondary-600 mb-3">Users by Role</h3>
                <div class="space-y-2">
                    {% for role_data in user_stats.users_by_role %}
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-secondary-700 capitalize">{{ role_data.profile__role|default:"Unknown" }}</span>
                        <span class="text-sm font-medium text-secondary-900">{{ role_data.count }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Listing Statistics -->
<div class="mb-8">
    <h2 class="text-xl font-display font-semibold text-secondary-900 mb-4">Listing Statistics</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <!-- Total Listings -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Total</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ listing_stats.total_listings }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Listings -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Pending</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ listing_stats.pending_listings }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Approved Listings -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Approved</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ listing_stats.approved_listings }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rejected Listings -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Rejected</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ listing_stats.rejected_listings }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sold Listings -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Sold</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ listing_stats.sold_listings }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Listings by Property Type -->
    <div class="mt-6">
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Listings by Property Type</h3>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {% for type_data in listing_stats.listings_by_type %}
                    <div class="text-center p-4 bg-secondary-50 rounded-lg">
                        <p class="text-2xl font-bold text-secondary-900">{{ type_data.count }}</p>
                        <p class="text-sm text-secondary-600 capitalize">{{ type_data.property_type|default:"Unknown" }}</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Inquiry Statistics -->
<div class="mb-8">
    <h2 class="text-xl font-display font-semibold text-secondary-900 mb-4">Inquiry Statistics</h2>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Total Inquiries -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Total Inquiries</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ inquiry_stats.total_inquiries }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Unread Inquiries -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h8V9H4v2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Unread</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ inquiry_stats.unread_inquiries }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Responded Inquiries -->
        <div class="card">
            <div class="card-body">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-secondary-600">Responded</p>
                        <p class="text-2xl font-bold text-secondary-900">{{ inquiry_stats.responded_inquiries }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Platform Health -->
<div class="card">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-secondary-900">Platform Health Overview</h3>
        <p class="text-sm text-secondary-600">Key metrics and system status</p>
    </div>
    <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- User Engagement -->
            <div>
                <h4 class="text-md font-medium text-secondary-900 mb-3">User Engagement</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-secondary-600">Active Users Rate</span>
                        <span class="text-sm font-medium text-green-600">
                            {% widthratio user_stats.active_users user_stats.total_users 100 %}%
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-secondary-600">Inquiry Response Rate</span>
                        <span class="text-sm font-medium text-blue-600">
                            {% if inquiry_stats.total_inquiries > 0 %}
                                {% widthratio inquiry_stats.responded_inquiries inquiry_stats.total_inquiries 100 %}%
                            {% else %}
                                0%
                            {% endif %}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Listing Performance -->
            <div>
                <h4 class="text-md font-medium text-secondary-900 mb-3">Listing Performance</h4>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-secondary-600">Approval Rate</span>
                        <span class="text-sm font-medium text-green-600">
                            {% if listing_stats.total_listings > 0 %}
                                {% widthratio listing_stats.approved_listings listing_stats.total_listings 100 %}%
                            {% else %}
                                0%
                            {% endif %}
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-secondary-600">Pending Review</span>
                        <span class="text-sm font-medium text-yellow-600">{{ listing_stats.pending_listings }} listings</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}