<!-- Modern Alert Component -->
<!-- Usage: {% include 'components/alert.html' with type='success' title='Success!' message='Operation completed successfully.' %} -->

<div class="alert alert-{{ type|default:'info' }} {{ class|default:'' }}"
     {% if id %}id="{{ id }}"{% endif %}
     x-data="{ show: true }"
     x-show="show"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform scale-95"
     x-transition:enter-end="opacity-100 transform scale-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform scale-100"
     x-transition:leave-end="opacity-0 transform scale-95">
    
    <div class="flex items-start">
        <!-- Icon -->
        <div class="flex-shrink-0">
            {% if type == 'success' %}
                <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
            {% elif type == 'error' %}
                <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                </svg>
            {% elif type == 'warning' %}
                <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
            {% else %}
                <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                </svg>
            {% endif %}
        </div>
        
        <!-- Content -->
        <div class="ml-3 flex-1">
            {% if title %}
                <h3 class="text-sm font-medium 
                    {% if type == 'success' %}text-green-800
                    {% elif type == 'error' %}text-red-800
                    {% elif type == 'warning' %}text-yellow-800
                    {% else %}text-blue-800{% endif %}">
                    {{ title }}
                </h3>
            {% endif %}
            
            {% if message %}
                <div class="{% if title %}mt-1{% endif %} text-sm 
                    {% if type == 'success' %}text-green-700
                    {% elif type == 'error' %}text-red-700
                    {% elif type == 'warning' %}text-yellow-700
                    {% else %}text-blue-700{% endif %}">
                    {{ message|safe }}
                </div>
            {% endif %}
            
            {% if content %}
                <div class="{% if title %}mt-1{% endif %}">
                    {{ content|safe }}
                </div>
            {% endif %}
            
            {% if actions %}
                <div class="mt-3">
                    {{ actions|safe }}
                </div>
            {% endif %}
        </div>
        
        <!-- Close button -->
        {% if dismissible|default:True %}
            <div class="ml-auto pl-3">
                <button @click="show = false" 
                        class="inline-flex 
                            {% if type == 'success' %}text-green-400 hover:text-green-600
                            {% elif type == 'error' %}text-red-400 hover:text-red-600
                            {% elif type == 'warning' %}text-yellow-400 hover:text-yellow-600
                            {% else %}text-blue-400 hover:text-blue-600{% endif %}
                            focus:outline-none focus:ring-2 focus:ring-offset-2 
                            {% if type == 'success' %}focus:ring-green-500
                            {% elif type == 'error' %}focus:ring-red-500
                            {% elif type == 'warning' %}focus:ring-yellow-500
                            {% else %}focus:ring-blue-500{% endif %}
                            rounded-md p-1.5">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        {% endif %}
    </div>
</div>
