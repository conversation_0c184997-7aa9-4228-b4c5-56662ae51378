<!-- Modern Form Field Component -->
<!-- Usage: {% include 'components/form_field.html' with field=form.email label='Email Address' %} -->

<div class="form-field {{ wrapper_class|default:'' }}">
    {% if label %}
        <label for="{{ field.id_for_label }}" class="form-label {{ label_class|default:'' }}">
            {{ label }}
            {% if required %}
                <span class="text-red-500 ml-1">*</span>
            {% endif %}
        </label>
    {% endif %}
    
    {% if help_text %}
        <p class="text-sm text-secondary-600 mb-2">{{ help_text }}</p>
    {% endif %}
    
    <div class="relative">
        {% if field.field.widget.input_type == 'textarea' %}
            {{ field|add_class:"form-textarea" }}
        {% elif field.field.widget.input_type == 'select' %}
            {{ field|add_class:"form-select" }}
        {% else %}
            {{ field|add_class:"form-input" }}
        {% endif %}
        
        {% if icon %}
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg class="w-5 h-5 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {{ icon|safe }}
                </svg>
            </div>
        {% endif %}
        
        {% if field.field.widget.input_type == 'password' %}
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <button type="button" 
                        x-data="{ show: false }"
                        @click="show = !show; $refs.input.type = show ? 'text' : 'password'"
                        class="text-secondary-400 hover:text-secondary-600 focus:outline-none">
                    <svg x-show="!show" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    <svg x-show="show" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                    </svg>
                </button>
            </div>
        {% endif %}
    </div>
    
    {% if field.errors %}
        <div class="form-error">
            {% for error in field.errors %}
                <div class="flex items-center mt-1">
                    <svg class="w-4 h-4 mr-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <span>{{ error }}</span>
                </div>
            {% endfor %}
        </div>
    {% endif %}
    
    {% if field.help_text %}
        <p class="mt-1 text-sm text-secondary-500">{{ field.help_text }}</p>
    {% endif %}
</div>
