{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Create Account - LandHub{% endblock %}

{% block main_class %}flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-primary-50 via-white to-secondary-50{% endblock %}

{% block content %}
<div class="w-full max-w-lg space-y-8 animate-fade-in">
    <!-- Floating Background Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse" style="animation-delay: 2s;"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-primary-100 to-secondary-100 rounded-full mix-blend-multiply filter blur-2xl opacity-30 animate-pulse" style="animation-delay: 4s;"></div>
    </div>

    <!-- Header -->
    <div class="text-center relative z-10">
        <div class="flex justify-center mb-8">
            <div class="relative group">
                <div class="absolute -inset-2 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative w-20 h-20 bg-gradient-to-br from-primary-600 to-secondary-700 rounded-2xl flex items-center justify-center shadow-2xl transform group-hover:scale-105 transition-all duration-300">
                    <svg class="w-10 h-10 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                </div>
            </div>
        </div>
        <h1 class="text-4xl font-display font-bold text-secondary-900 mb-3 tracking-tight">Join LandHub</h1>
        <p class="text-lg text-secondary-600 font-medium">Create your account and start exploring land opportunities</p>
        <div class="w-20 h-1 bg-gradient-to-r from-primary-600 to-secondary-400 rounded-full mx-auto mt-4"></div>
    </div>

    <!-- Registration Form Card -->
    <div class="relative z-10">
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden transform hover:scale-[1.01] transition-all duration-500">
            <!-- Card Header with Gradient -->
            <div class="bg-gradient-to-r from-primary-600 to-secondary-700 px-8 py-6">
                <div class="flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                    </svg>
                    <h2 class="text-white font-semibold text-lg">Create Account</h2>
                </div>
            </div>

            <div class="px-8 py-8">
                <form method="post"
                      hx-post="{% url 'auth:register' %}"
                      hx-target="#form-container"
                      hx-swap="outerHTML"
                      x-data="{
                          loading: false,
                          showPassword: false,
                          showConfirmPassword: false,
                          formValid: false,
                          firstName: '',
                          lastName: '',
                          username: '',
                          email: '',
                          password: '',
                          confirmPassword: '',
                          agreeTerms: false
                      }"
                      @htmx:before-request="loading = true"
                      @htmx:after-request="loading = false"
                      @input="formValid = firstName.length > 0 && lastName.length > 0 && username.length > 0 && email.length > 0 && password.length > 0 && confirmPassword.length > 0 && agreeTerms">
                    <div id="form-container">
                        {% csrf_token %}

                        <!-- Name Fields -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                            <div class="space-y-3 group">
                                <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-semibold text-secondary-700 group-focus-within:text-primary-600 transition-colors duration-200">
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                        </svg>
                                        <span>First Name *</span>
                                    </div>
                                </label>
                                <div class="relative">
                                    {{ form.first_name|add_class:"w-full px-4 py-3 bg-secondary-50/50 border-2 border-secondary-200 rounded-xl text-secondary-900 placeholder-secondary-400 focus:outline-none focus:ring-0 focus:border-primary-500 focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-secondary-300 hover:bg-white/80" }}
                                    <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 opacity-0 group-focus-within:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
                                </div>
                                {% if form.first_name.errors %}
                                    <div class="flex items-center space-x-2 text-red-600 text-sm animate-shake bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                                        <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span>{{ form.first_name.errors.0 }}</span>
                                    </div>
                                {% endif %}
                            </div>

                            <div class="space-y-3 group">
                                <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-semibold text-secondary-700 group-focus-within:text-primary-600 transition-colors duration-200">
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                        </svg>
                                        <span>Last Name *</span>
                                    </div>
                                </label>
                                <div class="relative">
                                    {{ form.last_name|add_class:"w-full px-4 py-3 bg-secondary-50/50 border-2 border-secondary-200 rounded-xl text-secondary-900 placeholder-secondary-400 focus:outline-none focus:ring-0 focus:border-primary-500 focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-secondary-300 hover:bg-white/80" }}
                                    <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 opacity-0 group-focus-within:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
                                </div>
                                {% if form.last_name.errors %}
                                    <div class="flex items-center space-x-2 text-red-600 text-sm animate-shake bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                                        <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span>{{ form.last_name.errors.0 }}</span>
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Username Field -->
                        <div class="space-y-3 group mb-6">
                            <label for="{{ form.username.id_for_label }}" class="block text-sm font-semibold text-secondary-700 group-focus-within:text-primary-600 transition-colors duration-200">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                                    </svg>
                                    <span>Username *</span>
                                </div>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                                    <svg class="w-5 h-5 text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                                    </svg>
                                </div>
                                {{ form.username|add_class:"w-full pl-12 pr-4 py-4 bg-secondary-50/50 border-2 border-secondary-200 rounded-xl text-secondary-900 placeholder-secondary-400 focus:outline-none focus:ring-0 focus:border-primary-500 focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-secondary-300 hover:bg-white/80" }}
                                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 opacity-0 group-focus-within:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
                            </div>
                            {% if form.username.errors %}
                                <div class="flex items-center space-x-2 text-red-600 text-sm animate-shake bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                                    <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>{{ form.username.errors.0 }}</span>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Email Field -->
                        <div class="space-y-3 group mb-6">
                            <label for="{{ form.email.id_for_label }}" class="block text-sm font-semibold text-secondary-700 group-focus-within:text-primary-600 transition-colors duration-200">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
                                    </svg>
                                    <span>Email Address *</span>
                                </div>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                                    <svg class="w-5 h-5 text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"/>
                                    </svg>
                                </div>
                                {{ form.email|add_class:"w-full pl-12 pr-4 py-4 bg-secondary-50/50 border-2 border-secondary-200 rounded-xl text-secondary-900 placeholder-secondary-400 focus:outline-none focus:ring-0 focus:border-primary-500 focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-secondary-300 hover:bg-white/80" }}
                                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 opacity-0 group-focus-within:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
                            </div>
                            {% if form.email.errors %}
                                <div class="flex items-center space-x-2 text-red-600 text-sm animate-shake bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                                    <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>{{ form.email.errors.0 }}</span>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Role Field -->
                        <div class="space-y-3 group mb-6">
                            <label for="{{ form.role.id_for_label }}" class="block text-sm font-semibold text-secondary-700 group-focus-within:text-primary-600 transition-colors duration-200">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8"/>
                                    </svg>
                                    <span>I am a *</span>
                                </div>
                            </label>
                            <div class="relative">
                                {{ form.role|add_class:"w-full px-4 py-4 bg-secondary-50/50 border-2 border-secondary-200 rounded-xl text-secondary-900 focus:outline-none focus:ring-0 focus:border-primary-500 focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-secondary-300 hover:bg-white/80 appearance-none cursor-pointer" }}
                                <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                    <svg class="w-5 h-5 text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                                    </svg>
                                </div>
                                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 opacity-0 group-focus-within:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
                            </div>
                            {% if form.role.errors %}
                                <div class="flex items-center space-x-2 text-red-600 text-sm animate-shake bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                                    <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>{{ form.role.errors.0 }}</span>
                                </div>
                            {% endif %}
                            <p class="text-sm text-secondary-500 font-medium">Choose your role to get personalized features</p>
                        </div>

                        <!-- Phone Field -->
                        <div class="space-y-3 group mb-6">
                            <label for="{{ form.phone.id_for_label }}" class="block text-sm font-semibold text-secondary-700 group-focus-within:text-primary-600 transition-colors duration-200">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                    <span>Phone Number</span>
                                    <span class="text-xs text-secondary-400">(Optional)</span>
                                </div>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                                    <svg class="w-5 h-5 text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                </div>
                                {{ form.phone|add_class:"w-full pl-12 pr-4 py-4 bg-secondary-50/50 border-2 border-secondary-200 rounded-xl text-secondary-900 placeholder-secondary-400 focus:outline-none focus:ring-0 focus:border-primary-500 focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-secondary-300 hover:bg-white/80" }}
                                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 opacity-0 group-focus-within:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
                            </div>
                            {% if form.phone.errors %}
                                <div class="flex items-center space-x-2 text-red-600 text-sm animate-shake bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                                    <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>{{ form.phone.errors.0 }}</span>
                                </div>
                            {% endif %}
                            <p class="text-sm text-secondary-500 font-medium">Optional - for better communication</p>
                        </div>

                        <!-- Password Fields -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                            <div class="space-y-3 group">
                                <label for="{{ form.password1.id_for_label }}" class="block text-sm font-semibold text-secondary-700 group-focus-within:text-primary-600 transition-colors duration-200">
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                        </svg>
                                        <span>Password *</span>
                                    </div>
                                </label>
                                <div class="relative" x-data="{ showPassword: false }">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                                        <svg class="w-5 h-5 text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                        </svg>
                                    </div>
                                    <input :type="showPassword ? 'text' : 'password'"
                                           name="{{ form.password1.name }}"
                                           id="{{ form.password1.id_for_label }}"
                                           x-model="password"
                                           class="w-full pl-12 pr-12 py-4 bg-secondary-50/50 border-2 border-secondary-200 rounded-xl text-secondary-900 placeholder-secondary-400 focus:outline-none focus:ring-0 focus:border-primary-500 focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-secondary-300 hover:bg-white/80"
                                           placeholder="Create a strong password"
                                           required>
                                    <button type="button"
                                            @click="showPassword = !showPassword"
                                            class="absolute inset-y-0 right-0 pr-4 flex items-center text-secondary-400 hover:text-primary-500 focus:outline-none z-10 transition-colors duration-200 group">
                                        <div class="p-1 rounded-lg hover:bg-primary-50 transition-colors duration-200">
                                            <svg x-show="!showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                            <svg x-show="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                                            </svg>
                                        </div>
                                    </button>
                                    <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 opacity-0 group-focus-within:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
                                </div>
                                {% if form.password1.errors %}
                                    <div class="flex items-center space-x-2 text-red-600 text-sm animate-shake bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                                        <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span>{{ form.password1.errors.0 }}</span>
                                    </div>
                                {% endif %}
                            </div>

                            <div class="space-y-3 group">
                                <label for="{{ form.password2.id_for_label }}" class="block text-sm font-semibold text-secondary-700 group-focus-within:text-primary-600 transition-colors duration-200">
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span>Confirm Password *</span>
                                    </div>
                                </label>
                                <div class="relative" x-data="{ showConfirmPassword: false }">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                                        <svg class="w-5 h-5 text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                    </div>
                                    <input :type="showConfirmPassword ? 'text' : 'password'"
                                           name="{{ form.password2.name }}"
                                           id="{{ form.password2.id_for_label }}"
                                           x-model="confirmPassword"
                                           class="w-full pl-12 pr-12 py-4 bg-secondary-50/50 border-2 border-secondary-200 rounded-xl text-secondary-900 placeholder-secondary-400 focus:outline-none focus:ring-0 focus:border-primary-500 focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-secondary-300 hover:bg-white/80"
                                           placeholder="Confirm your password"
                                           required>
                                    <button type="button"
                                            @click="showConfirmPassword = !showConfirmPassword"
                                            class="absolute inset-y-0 right-0 pr-4 flex items-center text-secondary-400 hover:text-primary-500 focus:outline-none z-10 transition-colors duration-200 group">
                                        <div class="p-1 rounded-lg hover:bg-primary-50 transition-colors duration-200">
                                            <svg x-show="!showConfirmPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                            <svg x-show="showConfirmPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                                            </svg>
                                        </div>
                                    </button>
                                    <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 opacity-0 group-focus-within:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
                                </div>
                                {% if form.password2.errors %}
                                    <div class="flex items-center space-x-2 text-red-600 text-sm animate-shake bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                                        <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span>{{ form.password2.errors.0 }}</span>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                        <!-- Terms and Conditions -->
                        <div class="mb-8">
                            <div class="flex items-start space-x-3 group">
                                <div class="relative">
                                    <input type="checkbox"
                                           id="terms"
                                           name="terms"
                                           x-model="agreeTerms"
                                           class="h-5 w-5 text-primary-600 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 border-2 border-secondary-300 rounded-md transition-all duration-200 hover:border-primary-400 cursor-pointer"
                                           required>
                                    <div class="absolute inset-0 rounded-md bg-primary-100 opacity-0 group-hover:opacity-20 transition-opacity duration-200 pointer-events-none"></div>
                                </div>
                                <label for="terms" class="block text-sm text-secondary-700 cursor-pointer leading-relaxed">
                                    I agree to the
                                    <a href="#" class="text-primary-600 hover:text-primary-700 font-semibold transition-all duration-200 hover:underline decoration-2 underline-offset-2">Terms of Service</a>
                                    and
                                    <a href="#" class="text-primary-600 hover:text-primary-700 font-semibold transition-all duration-200 hover:underline decoration-2 underline-offset-2">Privacy Policy</a>
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit"
                                class="relative w-full py-4 px-6 bg-gradient-to-r from-primary-600 to-secondary-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-primary-500/50 transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none overflow-hidden group"
                                :class="{ 'loading': loading }"
                                :disabled="loading || !formValid">
                            <!-- Button Background Animation -->
                            <div class="absolute inset-0 bg-gradient-to-r from-primary-700 to-secondary-800 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                            <!-- Button Content -->
                            <div class="relative flex items-center justify-center">
                                <span x-show="!loading" class="flex items-center space-x-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                                    </svg>
                                    <span>Create Account</span>
                                </span>
                                <span x-show="loading" class="flex items-center space-x-3">
                                    <div class="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                    <span>Creating account...</span>
                                </span>
                            </div>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Sign In Link -->
    <div class="text-center relative z-10">
        <div class="bg-white/60 backdrop-blur-sm rounded-2xl px-6 py-4 border border-white/30 shadow-lg">
            <p class="text-sm text-secondary-600">
                Already have an account?
                <a href="{% url 'auth:login' %}"
                   class="font-semibold text-primary-600 hover:text-primary-700 transition-all duration-200 hover:underline decoration-2 underline-offset-2 ml-1">
                    Sign in instead
                </a>
            </p>
        </div>
    </div>
</div>

<!-- Custom Styles and Animations -->
<style>
    @keyframes fade-in {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    .animate-fade-in {
        animation: fade-in 0.6s ease-out;
    }

    .animate-shake {
        animation: shake 0.5s ease-in-out;
    }

    /* Custom focus ring for better accessibility */
    .focus-ring:focus {
        outline: 2px solid transparent;
        outline-offset: 2px;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
    }

    /* Smooth transitions for all interactive elements */
    * {
        transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Custom select dropdown styling */
    select {
        background-image: none;
    }
</style>
{% endblock %}