<!-- Modern Modal Component -->
<!-- Usage: {% include 'components/modal.html' with id='my-modal' title='Modal Title' %} -->

<div x-data="{ open: false }" 
     x-on:open-modal-{{ id|default:'modal' }}.window="open = true"
     x-on:close-modal-{{ id|default:'modal' }}.window="open = false">
    
    <!-- Modal Backdrop -->
    <div x-show="open" 
         x-cloak
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 overflow-y-auto"
         aria-labelledby="modal-title" 
         role="dialog" 
         aria-modal="true">
        
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div x-show="open"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 @click="open = false"
                 class="fixed inset-0 bg-secondary-900 bg-opacity-75 transition-opacity"
                 aria-hidden="true"></div>

            <!-- This element is to trick the browser into centering the modal contents. -->
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <!-- Modal panel -->
            <div x-show="open"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 class="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle {{ size|default:'sm:max-w-lg' }} sm:w-full">
                
                <!-- Modal Header -->
                {% if title or closable %}
                    <div class="bg-white px-6 py-4 border-b border-secondary-200">
                        <div class="flex items-center justify-between">
                            {% if title %}
                                <h3 class="text-lg font-semibold text-secondary-900" id="modal-title">
                                    {{ title }}
                                </h3>
                            {% endif %}
                            {% if closable|default:True %}
                                <button @click="open = false" 
                                        class="text-secondary-400 hover:text-secondary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-lg p-1">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            {% endif %}
                        </div>
                        {% if subtitle %}
                            <p class="mt-1 text-sm text-secondary-600">{{ subtitle }}</p>
                        {% endif %}
                    </div>
                {% endif %}
                
                <!-- Modal Body -->
                <div class="bg-white px-6 py-4 {{ body_class|default:'' }}">
                    {% if content %}
                        {{ content|safe }}
                    {% else %}
                        {% block modal_content %}
                            <p class="text-secondary-600">Modal content goes here...</p>
                        {% endblock %}
                    {% endif %}
                </div>
                
                <!-- Modal Footer -->
                {% if footer_content or actions %}
                    <div class="bg-secondary-50 px-6 py-4 border-t border-secondary-200">
                        {% if footer_content %}
                            {{ footer_content|safe }}
                        {% endif %}
                        {% if actions %}
                            <div class="flex items-center justify-end space-x-3">
                                {{ actions|safe }}
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- JavaScript helper for opening modals -->
<script>
    function openModal(modalId) {
        window.dispatchEvent(new CustomEvent('open-modal-' + modalId));
    }
    
    function closeModal(modalId) {
        window.dispatchEvent(new CustomEvent('close-modal-' + modalId));
    }
</script>
