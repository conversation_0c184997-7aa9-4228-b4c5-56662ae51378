{% extends 'dashboards/base_dashboard.html' %}

{% block dashboard_title %}Seller Dashboard{% endblock %}
{% block active_nav_item %}overview{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Seller Hub{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='#overview' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Overview' item_key='overview' %}

    <!-- My Listings -->
    {% include 'components/sidebar_nav_item.html' with href='#listings' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>' label='My Listings' item_key='listings' badge_count=8 %}

    <!-- Create Listing -->
    {% include 'components/sidebar_nav_item.html' with href='#create' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>' label='Create Listing' item_key='create' %}

    <!-- Inquiries -->
    {% include 'components/sidebar_nav_item.html' with href='#inquiries' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>' label='Inquiries' item_key='inquiries' badge_count=3 %}

    <!-- Performance -->
    {% include 'components/sidebar_nav_item.html' with href='#performance' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Performance' item_key='performance' %}

    <!-- Divider -->
    <div class="border-t border-secondary-200 my-4"></div>

    <!-- Help & Support -->
    {% include 'components/sidebar_nav_item.html' with href='#help' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>' label='Help & Support' item_key='help' %}

    <!-- Seller Resources -->
    {% include 'components/sidebar_nav_item.html' with href='#resources' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>' label='Seller Resources' item_key='resources' %}
</div>
{% endblock %}

{% block page_title %}Seller Overview{% endblock %}
{% block page_description %}Manage your land listings and connect with potential buyers{% endblock %}

{% block page_actions %}
<button class="btn btn-secondary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
    </svg>
    View Analytics
</button>
<button class="btn btn-primary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
    </svg>
    Create New Listing
</button>
{% endblock %}

{% block dashboard_content %}
<!-- Quick Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Listings -->
    <div class="card hover:shadow-lg transition-shadow duration-200">
        <div class="card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-secondary-600">Total Listings</p>
                    <p class="text-2xl font-bold text-secondary-900">8</p>
                    <p class="text-xs text-green-600">+2 this month</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Listings -->
    <div class="card hover:shadow-lg transition-shadow duration-200">
        <div class="card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-secondary-600">Active Listings</p>
                    <p class="text-2xl font-bold text-secondary-900">6</p>
                    <p class="text-xs text-green-600">75% approval rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Inquiries -->
    <div class="card hover:shadow-lg transition-shadow duration-200">
        <div class="card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-secondary-600">Total Inquiries</p>
                    <p class="text-2xl font-bold text-secondary-900">24</p>
                    <p class="text-xs text-blue-600">3 new this week</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Average Views -->
    <div class="card hover:shadow-lg transition-shadow duration-200">
        <div class="card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-secondary-600">Avg. Views/Listing</p>
                    <p class="text-2xl font-bold text-secondary-900">142</p>
                    <p class="text-xs text-green-600">+18% this month</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Recent Listings -->
    <div class="lg:col-span-2">
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-secondary-900">Recent Listings</h3>
                        <p class="text-sm text-secondary-600">Your latest property listings and their status</p>
                    </div>
                    <button class="btn btn-secondary btn-sm">View All</button>
                </div>
            </div>
            <div class="card-body">
                <div class="space-y-4">
                    <!-- Listing items will be implemented in later tasks -->
                    <div class="flex items-center space-x-4 p-4 bg-secondary-50 rounded-lg">
                        <div class="w-16 h-16 bg-secondary-200 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-secondary-900">Premium Agricultural Land - 50 Acres</h4>
                            <p class="text-xs text-secondary-500">Created 2 days ago</p>
                            <div class="flex items-center mt-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                                <span class="ml-2 text-xs text-secondary-500">142 views</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-semibold text-secondary-900">$125,000</p>
                            <p class="text-xs text-secondary-500">$2,500/acre</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4 p-4 bg-secondary-50 rounded-lg">
                        <div class="w-16 h-16 bg-secondary-200 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-secondary-900">Residential Development Plot - 5 Acres</h4>
                            <p class="text-xs text-secondary-500">Created 1 week ago</p>
                            <div class="flex items-center mt-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Pending Review
                                </span>
                                <span class="ml-2 text-xs text-secondary-500">0 views</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-semibold text-secondary-900">$85,000</p>
                            <p class="text-xs text-secondary-500">$17,000/acre</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4 p-4 bg-secondary-50 rounded-lg">
                        <div class="w-16 h-16 bg-secondary-200 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-secondary-900">Recreational Hunting Land - 25 Acres</h4>
                            <p class="text-xs text-secondary-500">Created 2 weeks ago</p>
                            <div class="flex items-center mt-2">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                                <span class="ml-2 text-xs text-secondary-500">89 views</span>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-semibold text-secondary-900">$75,000</p>
                            <p class="text-xs text-secondary-500">$3,000/acre</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions & Recent Inquiries -->
    <div class="space-y-6">
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Quick Actions</h3>
            </div>
            <div class="card-body space-y-3">
                <button class="w-full btn btn-primary btn-md justify-start">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create New Listing
                </button>
                
                <button class="w-full btn btn-secondary btn-md justify-start">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    View Inquiries
                </button>
                
                <button class="w-full btn btn-secondary btn-md justify-start">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    View Performance
                </button>
            </div>
        </div>

        <!-- Recent Inquiries -->
        <div class="card">
            <div class="card-header">
                <h3 class="text-lg font-semibold text-secondary-900">Recent Inquiries</h3>
            </div>
            <div class="card-body">
                <div class="space-y-3">
                    <div class="flex items-center space-x-3 p-3 bg-secondary-50 rounded-lg">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-secondary-900">John Smith</p>
                            <p class="text-xs text-secondary-500">Agricultural Land inquiry</p>
                            <p class="text-xs text-secondary-500">2 hours ago</p>
                        </div>
                        <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                    </div>
                    
                    <div class="flex items-center space-x-3 p-3 bg-secondary-50 rounded-lg">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-secondary-900">Sarah Johnson</p>
                            <p class="text-xs text-secondary-500">Recreational Land inquiry</p>
                            <p class="text-xs text-secondary-500">1 day ago</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-3 p-3 bg-secondary-50 rounded-lg">
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-secondary-900">Mike Davis</p>
                            <p class="text-xs text-secondary-500">Residential Plot inquiry</p>
                            <p class="text-xs text-secondary-500">3 days ago</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}