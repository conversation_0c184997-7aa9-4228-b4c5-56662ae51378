{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Profile Settings - LandHub{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto space-y-8">
    <!-- Profile Header -->
    <div class="card card-elevated">
        <div class="card-header">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    {% if user.profile.avatar %}
                        <img src="{{ user.profile.avatar.url }}" alt="Profile Avatar" class="w-16 h-16 rounded-full object-cover border-4 border-white shadow-lg">
                    {% else %}
                        <div class="w-16 h-16 rounded-full bg-gradient-primary flex items-center justify-center shadow-lg">
                            <span class="text-white font-display font-bold text-xl">{{ user.first_name.0|default:user.username.0|upper }}</span>
                        </div>
                    {% endif %}
                    <div>
                        <h1 class="text-2xl font-display font-bold text-secondary-900">{{ user.get_full_name|default:user.username }}</h1>
                        <p class="text-secondary-600">{{ user.email }}</p>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 mt-2">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8"></path>
                            </svg>
                            {{ user.profile.get_role_display }}
                        </span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{% url 'auth:dashboard_redirect' %}" class="btn btn-secondary btn-md">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Form -->
    <div class="card card-elevated">
        <div class="card-header">
            <h2 class="text-lg font-semibold text-secondary-900">Personal Information</h2>
            <p class="text-sm text-secondary-600 mt-1">Update your personal details and contact information</p>
        </div>
        <div class="card-body">
            <form method="post"
                  enctype="multipart/form-data"
                  hx-post="{% url 'auth:profile' %}"
                  hx-target="#form-container"
                  hx-swap="outerHTML"
                  x-data="{ loading: false }"
                  @htmx:before-request="loading = true"
                  @htmx:after-request="loading = false">
                <div id="form-container">
                    {% csrf_token %}

                    <!-- Name Fields -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                First Name
                            </label>
                            {{ form.first_name|add_class:"form-input" }}
                            {% if form.first_name.errors %}
                                <div class="form-error">
                                    {% for error in form.first_name.errors %}
                                        <div class="flex items-center mt-1">
                                            <svg class="w-4 h-4 mr-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span>{{ error }}</span>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div>
                            <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                Last Name
                            </label>
                            {{ form.last_name|add_class:"form-input" }}
                            {% if form.last_name.errors %}
                                <div class="form-error">
                                    {% for error in form.last_name.errors %}
                                        <div class="flex items-center mt-1">
                                            <svg class="w-4 h-4 mr-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span>{{ error }}</span>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="{{ form.email.id_for_label }}" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                </svg>
                                Email Address
                            </label>
                            {{ form.email|add_class:"form-input" }}
                            {% if form.email.errors %}
                                <div class="form-error">
                                    {% for error in form.email.errors %}
                                        <div class="flex items-center mt-1">
                                            <svg class="w-4 h-4 mr-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span>{{ error }}</span>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div>
                            <label for="{{ form.phone.id_for_label }}" class="form-label">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                                Phone Number
                            </label>
                            {{ form.phone|add_class:"form-input" }}
                            {% if form.phone.errors %}
                                <div class="form-error">
                                    {% for error in form.phone.errors %}
                                        <div class="flex items-center mt-1">
                                            <svg class="w-4 h-4 mr-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                            <span>{{ error }}</span>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Bio -->
                    <div class="mb-6">
                        <label for="{{ form.bio.id_for_label }}" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
                            </svg>
                            Bio
                        </label>
                        {{ form.bio|add_class:"form-textarea" }}
                        {% if form.bio.errors %}
                            <div class="form-error">
                                {% for error in form.bio.errors %}
                                    <div class="flex items-center mt-1">
                                        <svg class="w-4 h-4 mr-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>{{ error }}</span>
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-sm text-secondary-500">Tell others about yourself and your interests in land</p>
                    </div>

                    <!-- Avatar Upload -->
                    <div class="mb-8">
                        <label for="{{ form.avatar.id_for_label }}" class="form-label">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            Profile Picture
                        </label>
                        <div class="flex items-center space-x-6">
                            {% if user.profile.avatar %}
                                <img src="{{ user.profile.avatar.url }}" alt="Current avatar" class="w-20 h-20 rounded-full object-cover border-4 border-secondary-200">
                            {% else %}
                                <div class="w-20 h-20 rounded-full bg-secondary-200 flex items-center justify-center">
                                    <svg class="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                            {% endif %}
                            <div class="flex-1">
                                {{ form.avatar|add_class:"form-input" }}
                                {% if form.avatar.errors %}
                                    <div class="form-error">
                                        {% for error in form.avatar.errors %}
                                            <div class="flex items-center mt-1">
                                                <svg class="w-4 h-4 mr-1 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                                </svg>
                                                <span>{{ error }}</span>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <p class="mt-1 text-sm text-secondary-500">JPG, PNG or GIF. Max size 2MB.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="card-footer">
            <div class="flex items-center justify-between">
                <a href="{% url 'auth:dashboard_redirect' %}" class="btn btn-secondary btn-md">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Dashboard
                </a>
                <button type="submit"
                        form="form-container"
                        class="btn btn-primary btn-md"
                        :class="{ 'loading': loading }"
                        :disabled="loading">
                    <span x-show="!loading">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Update Profile
                    </span>
                    <span x-show="loading" class="flex items-center">
                        <div class="spinner w-4 h-4 mr-2"></div>
                        Updating...
                    </span>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}