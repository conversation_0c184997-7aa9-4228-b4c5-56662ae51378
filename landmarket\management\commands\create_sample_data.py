from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from landmarket.models import UserProfile, Land, Inquiry, Favorite
from decimal import Decimal
import random


class Command(BaseCommand):
    help = 'Create sample data for testing admin dashboard'

    def handle(self, *args, **options):
        # Create admin user if not exists
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Admin',
                'last_name': 'User',
                'is_staff': True,
                'is_superuser': True
            }
        )
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            admin_user.profile.role = 'admin'
            admin_user.profile.save()
            self.stdout.write(f'Created admin user: {admin_user.username}')

        # Create sample sellers
        sellers = []
        for i in range(5):
            seller, created = User.objects.get_or_create(
                username=f'seller{i+1}',
                defaults={
                    'email': f'seller{i+1}@example.com',
                    'first_name': f'Seller',
                    'last_name': f'User{i+1}'
                }
            )
            if created:
                seller.set_password('password123')
                seller.save()
                seller.profile.role = 'seller'
                seller.profile.save()
                sellers.append(seller)
                self.stdout.write(f'Created seller: {seller.username}')

        # Create sample buyers
        buyers = []
        for i in range(8):
            buyer, created = User.objects.get_or_create(
                username=f'buyer{i+1}',
                defaults={
                    'email': f'buyer{i+1}@example.com',
                    'first_name': f'Buyer',
                    'last_name': f'User{i+1}'
                }
            )
            if created:
                buyer.set_password('password123')
                buyer.save()
                buyer.profile.role = 'buyer'
                buyer.profile.save()
                buyers.append(buyer)
                self.stdout.write(f'Created buyer: {buyer.username}')

        # Create sample land listings
        property_types = ['residential', 'commercial', 'agricultural', 'recreational']
        statuses = ['pending', 'approved', 'rejected', 'draft']
        locations = ['Austin, TX', 'Dallas, TX', 'Houston, TX', 'San Antonio, TX', 'Fort Worth, TX']
        
        for i in range(20):
            if sellers:
                owner = random.choice(sellers)
                Land.objects.get_or_create(
                    title=f'Beautiful Land Plot {i+1}',
                    defaults={
                        'owner': owner,
                        'description': f'This is a beautiful piece of land perfect for development. Located in a prime area with great potential.',
                        'price': Decimal(str(random.randint(50000, 500000))),
                        'size_acres': Decimal(str(random.uniform(1.0, 50.0))),
                        'location': random.choice(locations),
                        'address': f'{random.randint(100, 9999)} Land Street, {random.choice(locations)}',
                        'property_type': random.choice(property_types),
                        'status': random.choice(statuses),
                        'is_approved': random.choice([True, False])
                    }
                )
        
        self.stdout.write(f'Created 20 sample land listings')

        # Create sample inquiries
        lands = Land.objects.all()
        for i in range(15):
            if buyers and lands:
                buyer = random.choice(buyers)
                land = random.choice(lands)
                Inquiry.objects.get_or_create(
                    buyer=buyer,
                    land=land,
                    subject=f'Inquiry about {land.title}',
                    defaults={
                        'message': f'Hi, I am interested in this property. Can you provide more details?',
                        'is_read': random.choice([True, False])
                    }
                )
        
        self.stdout.write(f'Created 15 sample inquiries')
        self.stdout.write(self.style.SUCCESS('Sample data created successfully!'))