from django.urls import path
from .views import (
    home, admin_dashboard, seller_dashboard, buyer_dashboard,
    admin_user_management, admin_toggle_user_status,
    admin_listing_approval, admin_approve_listing, admin_reject_listing,
    admin_analytics
)

urlpatterns = [
    path('', home, name='home'),
    path('dashboard/', home, name='dashboard'),  # Temporary redirect
    path('admin-dashboard/', admin_dashboard, name='admin_dashboard'),
    path('seller-dashboard/', seller_dashboard, name='seller_dashboard'),
    path('buyer-dashboard/', buyer_dashboard, name='buyer_dashboard'),
    
    # Admin-specific URLs
    path('admin/users/', admin_user_management, name='admin_user_management'),
    path('admin/users/<int:user_id>/toggle-status/', admin_toggle_user_status, name='admin_toggle_user_status'),
    path('admin/listings/', admin_listing_approval, name='admin_listing_approval'),
    path('admin/listings/<int:listing_id>/approve/', admin_approve_listing, name='admin_approve_listing'),
    path('admin/listings/<int:listing_id>/reject/', admin_reject_listing, name='admin_reject_listing'),
    path('admin/analytics/', admin_analytics, name='admin_analytics'),
]
