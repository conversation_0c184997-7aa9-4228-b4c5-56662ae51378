{% extends 'base.html' %}

{% block title %}LandHub - Find Your Perfect Land{% endblock %}

{% block main_class %}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="relative overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50 py-20 lg:py-32">
    <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <div class="animate-fade-in">
                <h1 class="text-4xl md:text-6xl lg:text-7xl font-display font-bold text-secondary-900 mb-6">
                    Find Your Perfect
                    <span class="bg-gradient-primary bg-clip-text text-transparent">Land</span>
                </h1>
                <p class="text-xl md:text-2xl text-secondary-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                    Discover premium land opportunities for residential, commercial, agricultural, and recreational use.
                    Connect with verified sellers and find your ideal property today.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                    {% if not user.is_authenticated %}
                        <a href="{% url 'auth:register' %}" class="btn btn-primary btn-lg animate-bounce-subtle">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                            </svg>
                            Get Started Free
                        </a>
                        <a href="{% url 'auth:login' %}" class="btn btn-secondary btn-lg">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                            </svg>
                            Sign In
                        </a>
                    {% else %}
                        <a href="{% url 'auth:dashboard_redirect' %}" class="btn btn-primary btn-lg">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            Go to Dashboard
                        </a>
                        <a href="#listings" class="btn btn-secondary btn-lg">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Browse Listings
                        </a>
                    {% endif %}
                </div>

                <!-- Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-display font-bold text-primary-600 mb-2">1,000+</div>
                        <div class="text-sm md:text-base text-secondary-600">Active Listings</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-display font-bold text-primary-600 mb-2">500+</div>
                        <div class="text-sm md:text-base text-secondary-600">Happy Buyers</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-display font-bold text-primary-600 mb-2">50+</div>
                        <div class="text-sm md:text-base text-secondary-600">Cities Covered</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-display font-bold text-primary-600 mb-2">24/7</div>
                        <div class="text-sm md:text-base text-secondary-600">Support</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Features Section -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-display font-bold text-secondary-900 mb-4">
                Why Choose LandHub?
            </h2>
            <p class="text-xl text-secondary-600 max-w-2xl mx-auto">
                We make land buying and selling simple, secure, and efficient with our modern platform
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="card hover:scale-105 transition-transform duration-300">
                <div class="card-body text-center">
                    <div class="w-16 h-16 bg-gradient-primary rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-secondary-900 mb-3">Smart Search</h3>
                    <p class="text-secondary-600 leading-relaxed">
                        Find land properties with our advanced search filters by location, size, price, and land type. AI-powered recommendations help you discover perfect matches.
                    </p>
                </div>
            </div>

            <!-- Feature 2 -->
            <div class="card hover:scale-105 transition-transform duration-300">
                <div class="card-body text-center">
                    <div class="w-16 h-16 bg-gradient-primary rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-secondary-900 mb-3">Verified Listings</h3>
                    <p class="text-secondary-600 leading-relaxed">
                        All properties are verified by our expert team to ensure accuracy and legitimacy. Every listing includes detailed documentation and legal verification.
                    </p>
                </div>
            </div>

            <!-- Feature 3 -->
            <div class="card hover:scale-105 transition-transform duration-300">
                <div class="card-body text-center">
                    <div class="w-16 h-16 bg-gradient-primary rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a2 2 0 01-2-2v-6a2 2 0 012-2h8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-secondary-900 mb-3">Direct Connect</h3>
                    <p class="text-secondary-600 leading-relaxed">
                        Connect directly with property owners and sellers without intermediaries. Built-in messaging system ensures secure and efficient communication.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Role-based Information -->
<section class="py-20 bg-gradient-to-br from-secondary-50 to-primary-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-display font-bold text-secondary-900 mb-4">
                Choose Your Role
            </h2>
            <p class="text-xl text-secondary-600 max-w-2xl mx-auto">
                Join thousands of users who trust LandHub for their land transactions
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Buyer -->
            <div class="card card-elevated text-center hover:scale-105 transition-transform duration-300">
                <div class="card-body">
                    <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-semibold text-secondary-900 mb-4">Buyer</h3>
                    <p class="text-secondary-600 mb-6 leading-relaxed">
                        Search and filter land listings, save favorites, get market insights, and connect with verified sellers.
                    </p>
                    <ul class="text-left text-secondary-600 space-y-2 mb-6">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            Advanced search filters
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            Save favorite properties
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            Market analytics
                        </li>
                    </ul>
                    {% if not user.is_authenticated %}
                        <a href="{% url 'auth:register' %}?role=buyer" class="btn btn-primary btn-md w-full">
                            Join as Buyer
                        </a>
                    {% endif %}
                </div>
            </div>

            <!-- Seller -->
            <div class="card card-elevated text-center hover:scale-105 transition-transform duration-300">
                <div class="card-body">
                    <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-semibold text-secondary-900 mb-4">Seller</h3>
                    <p class="text-secondary-600 mb-6 leading-relaxed">
                        List your land properties, manage inquiries, track performance, and reach qualified buyers.
                    </p>
                    <ul class="text-left text-secondary-600 space-y-2 mb-6">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            Easy listing creation
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            Inquiry management
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            Performance analytics
                        </li>
                    </ul>
                    {% if not user.is_authenticated %}
                        <a href="{% url 'auth:register' %}?role=seller" class="btn btn-primary btn-md w-full">
                            Join as Seller
                        </a>
                    {% endif %}
                </div>
            </div>

            <!-- Admin -->
            <div class="card card-elevated text-center hover:scale-105 transition-transform duration-300">
                <div class="card-body">
                    <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-semibold text-secondary-900 mb-4">Admin</h3>
                    <p class="text-secondary-600 mb-6 leading-relaxed">
                        Manage users, approve listings, oversee platform operations, and ensure quality standards.
                    </p>
                    <ul class="text-left text-secondary-600 space-y-2 mb-6">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            User management
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            Listing approval
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            Platform analytics
                        </li>
                    </ul>
                    <div class="text-sm text-secondary-500">
                        Admin access by invitation only
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}