{% extends 'base.html' %}
{% load form_tags %}

{% block title %}Sign In - LandHub{% endblock %}

{% block main_class %}flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-primary-50 via-white to-secondary-50{% endblock %}

{% block content %}
<div class="w-full max-w-md space-y-8 animate-fade-in">
    <!-- Floating Background Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-100 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse" style="animation-delay: 2s;"></div>
    </div>

    <!-- Header -->
    <div class="text-center relative z-10">
        <div class="flex justify-center mb-8">
            <div class="relative group">
                <div class="absolute -inset-2 bg-gradient-to-r from-primary-600 to-primary-400 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative w-20 h-20 bg-gradient-to-br from-primary-600 to-primary-700 rounded-2xl flex items-center justify-center shadow-2xl transform group-hover:scale-105 transition-all duration-300">
                    <svg class="w-10 h-10 text-white" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                </div>
            </div>
        </div>
        <h1 class="text-4xl font-display font-bold text-secondary-900 mb-3 tracking-tight">Welcome back</h1>
        <p class="text-lg text-secondary-600 font-medium">Sign in to your LandHub account</p>
        <div class="w-16 h-1 bg-gradient-to-r from-primary-600 to-primary-400 rounded-full mx-auto mt-4"></div>
    </div>

    <!-- Login Form Card -->
    <div class="relative z-10">
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden transform hover:scale-[1.02] transition-all duration-500">
            <!-- Card Header with Gradient -->
            <div class="bg-gradient-to-r from-primary-600 to-primary-700 px-8 py-6">
                <div class="flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    <h2 class="text-white font-semibold text-lg">Secure Login</h2>
                </div>
            </div>

            <div class="px-8 py-8">
                <form method="post"
                      hx-post="{% url 'auth:login' %}"
                      hx-target="#form-container"
                      hx-swap="outerHTML"
                      x-data="{
                          loading: false,
                          showPassword: false,
                          formValid: false,
                          username: '',
                          password: ''
                      }"
                      @htmx:before-request="loading = true"
                      @htmx:after-request="loading = false"
                      @input="formValid = username.length > 0 && password.length > 0">
                    <div id="form-container">
                        {% csrf_token %}

                        <!-- Username Field -->
                        <div class="space-y-3 group">
                            <label for="{{ form.username.id_for_label }}" class="block text-sm font-semibold text-secondary-700 group-focus-within:text-primary-600 transition-colors duration-200">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                    <span>Username or Email</span>
                                </div>
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                                    <svg class="w-5 h-5 text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                </div>
                                {{ form.username|add_class:"w-full pl-12 pr-4 py-4 bg-secondary-50/50 border-2 border-secondary-200 rounded-xl text-secondary-900 placeholder-secondary-400 focus:outline-none focus:ring-0 focus:border-primary-500 focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-secondary-300 hover:bg-white/80" }}
                                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 opacity-0 group-focus-within:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
                            </div>
                            {% if form.username.errors %}
                                <div class="flex items-center space-x-2 text-red-600 text-sm animate-shake bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                                    <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>{{ form.username.errors.0 }}</span>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Password Field -->
                        <div class="space-y-3 group">
                            <label for="{{ form.password.id_for_label }}" class="block text-sm font-semibold text-secondary-700 group-focus-within:text-primary-600 transition-colors duration-200">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                    </svg>
                                    <span>Password</span>
                                </div>
                            </label>
                            <div class="relative" x-data="{ showPassword: false }">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                                    <svg class="w-5 h-5 text-secondary-400 group-focus-within:text-primary-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                                    </svg>
                                </div>
                                <input :type="showPassword ? 'text' : 'password'"
                                       name="{{ form.password.name }}"
                                       id="{{ form.password.id_for_label }}"
                                       x-model="password"
                                       class="w-full pl-12 pr-12 py-4 bg-secondary-50/50 border-2 border-secondary-200 rounded-xl text-secondary-900 placeholder-secondary-400 focus:outline-none focus:ring-0 focus:border-primary-500 focus:bg-white focus:shadow-lg transition-all duration-300 hover:border-secondary-300 hover:bg-white/80"
                                       placeholder="Enter your password"
                                       required>
                                <button type="button"
                                        @click="showPassword = !showPassword"
                                        class="absolute inset-y-0 right-0 pr-4 flex items-center text-secondary-400 hover:text-primary-500 focus:outline-none z-10 transition-colors duration-200 group">
                                    <div class="p-1 rounded-lg hover:bg-primary-50 transition-colors duration-200">
                                        <svg x-show="!showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                        </svg>
                                        <svg x-show="showPassword" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"/>
                                        </svg>
                                    </div>
                                </button>
                                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-primary-500 to-primary-600 opacity-0 group-focus-within:opacity-5 transition-opacity duration-300 pointer-events-none"></div>
                            </div>
                            {% if form.password.errors %}
                                <div class="flex items-center space-x-2 text-red-600 text-sm animate-shake bg-red-50 px-3 py-2 rounded-lg border border-red-200">
                                    <svg class="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    <span>{{ form.password.errors.0 }}</span>
                                </div>
                            {% endif %}
                        </div>

                        <!-- Remember Me & Forgot Password -->
                        <div class="flex items-center justify-between mb-8">
                            <div class="flex items-center group">
                                <div class="relative">
                                    <input type="checkbox"
                                           name="remember_me"
                                           id="remember_me"
                                           class="h-5 w-5 text-primary-600 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 border-2 border-secondary-300 rounded-md transition-all duration-200 hover:border-primary-400 cursor-pointer">
                                    <div class="absolute inset-0 rounded-md bg-primary-100 opacity-0 group-hover:opacity-20 transition-opacity duration-200 pointer-events-none"></div>
                                </div>
                                <label for="remember_me" class="ml-3 block text-sm font-medium text-secondary-700 hover:text-secondary-900 cursor-pointer transition-colors duration-200">
                                    Remember me
                                </label>
                            </div>
                            <a href="{% url 'auth:password_reset' %}"
                               class="text-sm text-primary-600 hover:text-primary-700 font-semibold transition-all duration-200 hover:underline decoration-2 underline-offset-2">
                                Forgot password?
                            </a>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit"
                                class="relative w-full py-4 px-6 bg-gradient-to-r from-primary-600 to-primary-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl focus:outline-none focus:ring-4 focus:ring-primary-500/50 transform hover:scale-[1.02] active:scale-[0.98] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none overflow-hidden group"
                                :class="{ 'loading': loading }"
                                :disabled="loading || !formValid">
                            <!-- Button Background Animation -->
                            <div class="absolute inset-0 bg-gradient-to-r from-primary-700 to-primary-800 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                            <!-- Button Content -->
                            <div class="relative flex items-center justify-center">
                                <span x-show="!loading" class="flex items-center space-x-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"/>
                                    </svg>
                                    <span>Sign In</span>
                                </span>
                                <span x-show="loading" class="flex items-center space-x-3">
                                    <div class="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                                    <span>Signing in...</span>
                                </span>
                            </div>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Sign Up Link -->
    <div class="text-center relative z-10">
        <div class="bg-white/60 backdrop-blur-sm rounded-2xl px-6 py-4 border border-white/30 shadow-lg">
            <p class="text-sm text-secondary-600">
                Don't have an account?
                <a href="{% url 'auth:register' %}"
                   class="font-semibold text-primary-600 hover:text-primary-700 transition-all duration-200 hover:underline decoration-2 underline-offset-2 ml-1">
                    Create your account
                </a>
            </p>
        </div>
    </div>

    <!-- Social Login (Future Enhancement) -->
    <div class="mt-8 relative z-10">
        <div class="relative">
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-secondary-300/50"></div>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="px-4 bg-white/80 backdrop-blur-sm text-secondary-500 font-medium rounded-full border border-secondary-200/50">Or continue with</span>
            </div>
        </div>

        <div class="mt-6 grid grid-cols-2 gap-4">
            <button class="group relative bg-white/80 backdrop-blur-sm border-2 border-secondary-200 hover:border-primary-300 rounded-xl px-4 py-3 text-sm font-semibold text-secondary-700 hover:text-primary-700 transition-all duration-300 hover:shadow-lg transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none" disabled>
                <div class="flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5" viewBox="0 0 24 24">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                    </svg>
                    <span>Google</span>
                </div>
                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-green-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </button>
            <button class="group relative bg-white/80 backdrop-blur-sm border-2 border-secondary-200 hover:border-primary-300 rounded-xl px-4 py-3 text-sm font-semibold text-secondary-700 hover:text-primary-700 transition-all duration-300 hover:shadow-lg transform hover:scale-[1.02] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none" disabled>
                <div class="flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    <span>Facebook</span>
                </div>
                <div class="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/10 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </button>
        </div>
    </div>
</div>

<!-- Custom Styles and Animations -->
<style>
    @keyframes fade-in {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }

    .animate-fade-in {
        animation: fade-in 0.6s ease-out;
    }

    .animate-shake {
        animation: shake 0.5s ease-in-out;
    }

    /* Custom focus ring for better accessibility */
    .focus-ring:focus {
        outline: 2px solid transparent;
        outline-offset: 2px;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
    }

    /* Smooth transitions for all interactive elements */
    * {
        transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    }
</style>
{% endblock %}