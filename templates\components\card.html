<!-- Modern Card Component -->
<!-- Usage: {% include 'components/card.html' with title='Card Title' elevated=True %} -->

<div class="card {% if elevated %}card-elevated{% endif %} {{ class|default:'' }}"
     {% if id %}id="{{ id }}"{% endif %}
     {% if x_data %}x-data="{{ x_data }}"{% endif %}
     {% if hx_get %}hx-get="{{ hx_get }}"{% endif %}
     {% if hx_target %}hx-target="{{ hx_target }}"{% endif %}>
    
    {% if title or header_content %}
        <div class="card-header">
            {% if title %}
                <h3 class="text-lg font-semibold text-secondary-900 {{ title_class|default:'' }}">{{ title }}</h3>
            {% endif %}
            {% if subtitle %}
                <p class="text-sm text-secondary-600 mt-1">{{ subtitle }}</p>
            {% endif %}
            {% if header_content %}
                {{ header_content|safe }}
            {% endif %}
            {% if header_actions %}
                <div class="flex items-center space-x-2 mt-3">
                    {{ header_actions|safe }}
                </div>
            {% endif %}
        </div>
    {% endif %}
    
    <div class="card-body {{ body_class|default:'' }}">
        {% if content %}
            {{ content|safe }}
        {% else %}
            {% block card_content %}
                <p class="text-secondary-600">Card content goes here...</p>
            {% endblock %}
        {% endif %}
    </div>
    
    {% if footer_content or actions %}
        <div class="card-footer">
            {% if footer_content %}
                {{ footer_content|safe }}
            {% endif %}
            {% if actions %}
                <div class="flex items-center justify-between">
                    {{ actions|safe }}
                </div>
            {% endif %}
        </div>
    {% endif %}
</div>
