{% extends 'dashboards/base_dashboard.html' %}

{% block dashboard_title %}Listing Approval{% endblock %}
{% block active_nav_item %}listings{% endblock %}

{% block sidebar_icon %}
<svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
</svg>
{% endblock %}

{% block sidebar_title %}Admin Panel{% endblock %}

{% block sidebar_navigation %}
<div x-data="sidebarNav()">
    <!-- Overview -->
    {% include 'components/sidebar_nav_item.html' with href='/admin-dashboard/' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Overview' item_key='overview' %}

    <!-- User Management -->
    {% include 'components/sidebar_nav_item.html' with href='/admin/users/' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path></svg>' label='User Management' item_key='users' %}

    <!-- Listing Approval -->
    {% include 'components/sidebar_nav_item.html' with href='/admin/listings/' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>' label='Listing Approval' item_key='listings' active=True %}

    <!-- Reports & Analytics -->
    {% include 'components/sidebar_nav_item.html' with href='/admin/analytics/' icon_svg='<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>' label='Reports & Analytics' item_key='reports' %}
</div>
{% endblock %}

{% block page_title %}Listing Approval Queue{% endblock %}
{% block page_description %}Review and approve land listings submitted by sellers{% endblock %}

{% block page_actions %}
<button class="btn btn-secondary btn-md">
    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
    </svg>
    Export Report
</button>
{% endblock %}

{% block dashboard_content %}
<!-- Filter Section -->
<div class="card mb-6">
    <div class="card-body">
        <form method="GET" class="space-y-4 md:space-y-0 md:flex md:items-end md:space-x-4" 
              hx-get="{% url 'admin_listing_approval' %}" 
              hx-target="#listing-list" 
              hx-trigger="submit, change from:select">
            
            <!-- Search Input -->
            <div class="flex-1">
                <label for="search" class="form-label">Search Listings</label>
                <input type="text" 
                       name="search" 
                       id="search" 
                       value="{{ search_query }}"
                       placeholder="Search by title, location, or seller..."
                       class="form-input">
            </div>

            <!-- Status Filter -->
            <div>
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    {% for value, label in status_choices %}
                        <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Submit Button -->
            <div>
                <button type="submit" class="btn btn-primary btn-md">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Filter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Listing List -->
<div id="listing-list">
    <div class="space-y-6">
        {% for listing in page_obj %}
        <div class="card hover:shadow-lg transition-shadow duration-200">
            <div class="card-body">
                <div class="flex flex-col lg:flex-row lg:items-start lg:space-x-6">
                    <!-- Listing Image -->
                    <div class="flex-shrink-0 mb-4 lg:mb-0">
                        {% if listing.images.exists %}
                            {% with listing.images.first as primary_image %}
                            <img src="{{ primary_image.image.url }}" 
                                 alt="{{ listing.title }}" 
                                 class="w-full lg:w-48 h-32 object-cover rounded-lg">
                            {% endwith %}
                        {% else %}
                            <div class="w-full lg:w-48 h-32 bg-secondary-200 rounded-lg flex items-center justify-center">
                                <svg class="w-12 h-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Listing Details -->
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-3">
                            <div>
                                <h3 class="text-lg font-semibold text-secondary-900 mb-1">{{ listing.title }}</h3>
                                <p class="text-sm text-secondary-600 mb-2">{{ listing.location }}</p>
                                <div class="flex items-center space-x-4 text-sm text-secondary-500">
                                    <span>${{ listing.price|floatformat:0 }}</span>
                                    <span>{{ listing.size_acres }} acres</span>
                                    <span class="capitalize">{{ listing.get_property_type_display }}</span>
                                </div>
                            </div>
                            <div id="listing-status-{{ listing.id }}">
                                {% include 'components/listing_status_badge.html' with listing=listing %}
                            </div>
                        </div>

                        <p class="text-sm text-secondary-700 mb-4 line-clamp-2">{{ listing.description }}</p>

                        <!-- Seller Info -->
                        <div class="flex items-center space-x-3 mb-4 p-3 bg-secondary-50 rounded-lg">
                            {% if listing.owner.profile.avatar %}
                                <img src="{{ listing.owner.profile.avatar.url }}" alt="Seller" class="w-8 h-8 rounded-full object-cover">
                            {% else %}
                                <div class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center">
                                    <span class="text-primary-600 font-medium text-sm">{{ listing.owner.first_name.0|default:listing.owner.username.0|upper }}</span>
                                </div>
                            {% endif %}
                            <div>
                                <p class="text-sm font-medium text-secondary-900">{{ listing.owner.get_full_name|default:listing.owner.username }}</p>
                                <p class="text-xs text-secondary-500">Seller • Joined {{ listing.owner.date_joined|date:"M Y" }}</p>
                            </div>
                        </div>

                        <!-- Admin Notes -->
                        {% if listing.admin_notes %}
                        <div class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <p class="text-sm text-yellow-800"><strong>Admin Notes:</strong> {{ listing.admin_notes }}</p>
                        </div>
                        {% endif %}

                        <!-- Action Buttons -->
                        {% if listing.status == 'pending' %}
                        <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-3 space-y-2 sm:space-y-0">
                            <!-- Admin Notes Input -->
                            <div class="flex-1">
                                <input type="text" 
                                       name="admin_notes"
                                       id="admin-notes-{{ listing.id }}" 
                                       placeholder="Add admin notes (optional)..."
                                       class="form-input text-sm">
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="flex space-x-2">
                                <button hx-post="{% url 'admin_approve_listing' listing.id %}"
                                        hx-include="[name='admin_notes']"
                                        hx-target="#listing-status-{{ listing.id }}"
                                        hx-swap="innerHTML"
                                        class="btn btn-sm bg-green-600 hover:bg-green-700 text-white">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Approve
                                </button>
                                
                                <button hx-post="{% url 'admin_reject_listing' listing.id %}"
                                        hx-include="[name='admin_notes']"
                                        hx-target="#listing-status-{{ listing.id }}"
                                        hx-swap="innerHTML"
                                        class="btn btn-sm btn-danger">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    Reject
                                </button>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Listing Metadata -->
                        <div class="mt-4 pt-4 border-t border-secondary-200 text-xs text-secondary-500">
                            <div class="flex justify-between">
                                <span>Created: {{ listing.created_at|date:"M d, Y g:i A" }}</span>
                                <span>Updated: {{ listing.updated_at|date:"M d, Y g:i A" }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="card">
            <div class="card-body text-center py-12">
                <svg class="mx-auto h-12 w-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                <p class="mt-2 text-sm text-secondary-500">No listings found matching your criteria.</p>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="mt-6 flex items-center justify-between">
        <div class="text-sm text-secondary-700">
            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
        </div>
        <div class="flex space-x-2">
            {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                   class="btn btn-secondary btn-sm">Previous</a>
            {% endif %}
            
            <span class="flex items-center px-3 py-1 text-sm text-secondary-700">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </span>
            
            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}" 
                   class="btn btn-secondary btn-sm">Next</a>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}