<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Navigation Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>
<body class="bg-secondary-50">
    <!-- Test the responsive sidebar component -->
    <div class="flex h-screen bg-secondary-50" x-data="{ sidebarOpen: false }">
        <!-- Desktop Sidebar -->
        <div class="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0 lg:z-30">
            <div class="flex flex-col flex-grow bg-white border-r border-secondary-200 shadow-sm overflow-y-auto">
                <!-- Sidebar Header -->
                <div class="flex items-center justify-between flex-shrink-0 px-6 py-4 border-b border-secondary-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg flex items-center justify-center shadow-sm">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h2 class="text-lg font-semibold text-secondary-900">Test Dashboard</h2>
                            <p class="text-xs text-secondary-500">Admin</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="flex-1 px-4 py-6 space-y-2" x-data="{ activeItem: 'overview' }">
                    <!-- Test navigation items -->
                    <a href="#" 
                       class="group flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                       :class="{
                           'bg-primary-100 text-primary-700 shadow-sm border border-primary-200': activeItem === 'overview',
                           'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100': activeItem !== 'overview'
                       }"
                       @click="activeItem = 'overview'">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200"
                                 :class="{
                                     'text-primary-600': activeItem === 'overview',
                                     'text-secondary-400 group-hover:text-secondary-600': activeItem !== 'overview'
                                 }">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <span class="truncate">Overview</span>
                        </div>
                    </a>

                    <a href="#" 
                       class="group flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                       :class="{
                           'bg-primary-100 text-primary-700 shadow-sm border border-primary-200': activeItem === 'users',
                           'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100': activeItem !== 'users'
                       }"
                       @click="activeItem = 'users'">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200"
                                 :class="{
                                     'text-primary-600': activeItem === 'users',
                                     'text-secondary-400 group-hover:text-secondary-600': activeItem !== 'users'
                                 }">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                            <span class="truncate">Users</span>
                        </div>
                        <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full transition-colors duration-200"
                              :class="{
                                  'bg-primary-200 text-primary-800': activeItem === 'users',
                                  'bg-secondary-200 text-secondary-700 group-hover:bg-secondary-300': activeItem !== 'users'
                              }">
                            3
                        </span>
                    </a>
                </nav>
            </div>
        </div>

        <!-- Mobile Sidebar Overlay -->
        <div x-show="sidebarOpen" 
             x-cloak
             class="fixed inset-0 flex z-40 lg:hidden"
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0">
            
            <!-- Backdrop -->
            <div class="fixed inset-0 bg-secondary-600 bg-opacity-75" 
                 @click="sidebarOpen = false"
                 aria-hidden="true"></div>

            <!-- Mobile Sidebar -->
            <div class="relative flex-1 flex flex-col max-w-xs w-full bg-white shadow-xl"
                 x-transition:enter="transition ease-in-out duration-300 transform"
                 x-transition:enter-start="-translate-x-full"
                 x-transition:enter-end="translate-x-0"
                 x-transition:leave="transition ease-in-out duration-300 transform"
                 x-transition:leave-start="translate-x-0"
                 x-transition:leave-end="-translate-x-full">
                
                <!-- Close Button -->
                <div class="absolute top-0 right-0 -mr-12 pt-2">
                    <button @click="sidebarOpen = false" 
                            class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white transition-colors duration-200 hover:bg-white hover:bg-opacity-20">
                        <span class="sr-only">Close sidebar</span>
                        <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Mobile Sidebar Content -->
                <div class="flex flex-col flex-grow pt-5 pb-4 overflow-y-auto">
                    <!-- Same content as desktop sidebar -->
                    <div class="flex items-center justify-between flex-shrink-0 px-6 pb-4 border-b border-secondary-200">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-r from-primary-600 to-primary-700 rounded-lg flex items-center justify-center shadow-sm">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h2 class="text-lg font-semibold text-secondary-900">Test Dashboard</h2>
                                <p class="text-xs text-secondary-500">Admin</p>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Navigation -->
                    <nav class="flex-1 px-4 py-6 space-y-2" x-data="{ activeItem: 'overview' }">
                        <!-- Same navigation items as desktop -->
                        <a href="#" 
                           class="group flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200"
                           :class="{
                               'bg-primary-100 text-primary-700 shadow-sm border border-primary-200': activeItem === 'overview',
                               'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100': activeItem !== 'overview'
                           }"
                           @click="activeItem = 'overview'; sidebarOpen = false">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200"
                                     :class="{
                                         'text-primary-600': activeItem === 'overview',
                                         'text-secondary-400 group-hover:text-secondary-600': activeItem !== 'overview'
                                     }">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <span class="truncate">Overview</span>
                            </div>
                        </a>

                        <a href="#" 
                           class="group flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200"
                           :class="{
                               'bg-primary-100 text-primary-700 shadow-sm border border-primary-200': activeItem === 'users',
                               'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100': activeItem !== 'users'
                           }"
                           @click="activeItem = 'users'; sidebarOpen = false">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 w-5 h-5 mr-3 transition-colors duration-200"
                                     :class="{
                                         'text-primary-600': activeItem === 'users',
                                         'text-secondary-400 group-hover:text-secondary-600': activeItem !== 'users'
                                     }">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                                <span class="truncate">Users</span>
                            </div>
                            <span class="inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full transition-colors duration-200"
                                  :class="{
                                      'bg-primary-200 text-primary-800': activeItem === 'users',
                                      'bg-secondary-200 text-secondary-700 group-hover:bg-secondary-300': activeItem !== 'users'
                                  }">
                                3
                            </span>
                        </a>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex flex-col flex-1 lg:pl-64">
            <!-- Mobile Header -->
            <div class="lg:hidden bg-white border-b border-secondary-200 px-4 py-3 shadow-sm">
                <div class="flex items-center justify-between">
                    <!-- Mobile Menu Button -->
                    <button @click="sidebarOpen = true" 
                            class="inline-flex items-center justify-center p-2 rounded-lg text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-colors duration-200">
                        <span class="sr-only">Open sidebar</span>
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>

                    <!-- Mobile Header Title -->
                    <h1 class="text-lg font-semibold text-secondary-900">Test Dashboard</h1>

                    <!-- Mobile User Avatar -->
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center">
                            <span class="text-primary-600 font-medium text-sm">A</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <main class="flex-1 overflow-y-auto bg-secondary-50">
                <div class="py-6">
                    <!-- Page Header -->
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold text-secondary-900">Dashboard Test</h1>
                                <p class="mt-1 text-sm text-secondary-600">Testing responsive sidebar navigation</p>
                            </div>
                        </div>
                    </div>

                    <!-- Page Content -->
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-6">
                        <div class="bg-white rounded-lg shadow p-6">
                            <h2 class="text-lg font-semibold text-secondary-900 mb-4">Responsive Sidebar Test</h2>
                            <div class="space-y-4">
                                <p class="text-secondary-600">
                                    This test page demonstrates the responsive sidebar navigation component:
                                </p>
                                <ul class="list-disc list-inside space-y-2 text-secondary-600">
                                    <li><strong>Desktop (lg+):</strong> Fixed sidebar visible on the left</li>
                                    <li><strong>Mobile/Tablet:</strong> Hamburger menu button that opens overlay sidebar</li>
                                    <li><strong>Alpine.js:</strong> Interactive toggle functionality</li>
                                    <li><strong>Tailwind CSS:</strong> Responsive utilities and smooth transitions</li>
                                    <li><strong>ShadCN UI:</strong> Consistent styling and component design</li>
                                </ul>
                                <div class="mt-6 p-4 bg-primary-50 rounded-lg">
                                    <p class="text-primary-800 font-medium">
                                        ✅ Resize your browser window or test on different devices to see the responsive behavior!
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>